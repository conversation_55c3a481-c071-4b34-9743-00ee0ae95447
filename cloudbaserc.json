{"envId": "zemuresume-4gjvx1wea78e3d1e", "version": "2.0", "$schema": "https://framework-1258016615.tcloudbaseapp.com/schema/latest.json", "functionRoot": "./cloudfunctions", "functions": [{"name": "ping", "config": {"timeout": 10, "runtime": "Nodejs18.15", "memorySize": 128, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, "timeout": 10, "runtime": "Nodejs18.15", "memorySize": 128, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, {"name": "jdWorker", "config": {"timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, "timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, {"name": "resumeWorker", "config": {"timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, "timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, {"name": "cvGenerator", "config": {"timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, "timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, {"name": "pdfProcessor", "config": {"timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, "timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, {"name": "userLogin", "config": {"timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production", "WECHAT_APPID": "wx4fa04593aaf3bb76", "WECHAT_SECRET": "4a90dd7e501e24f0489e2b83031e1100", "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76", "JWT_EXPIRES_IN": "7d"}}, "timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production", "WECHAT_APPID": "wx4fa04593aaf3bb76", "WECHAT_SECRET": "4a90dd7e501e24f0489e2b83031e1100", "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76", "JWT_EXPIRES_IN": "7d"}}, {"name": "tokenVerify", "config": {"timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production", "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76"}}, "timeout": 30, "runtime": "Nodejs18.15", "memorySize": 256, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production", "JWT_SECRET": "ai-resume-jwt-secret-key-2024-wx4fa04593aaf3bb76"}}, {"name": "resumePreviewGenerator", "config": {"timeout": 60, "runtime": "Nodejs18.15", "memorySize": 512, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}, "timeout": 60, "runtime": "Nodejs18.15", "memorySize": 512, "installDependency": true, "handler": "index.main", "environment": {"NODE_ENV": "production"}}], "cloudRun": {"services": [{"name": "resume-snapshot", "path": "./cloud-run/resume-snapshot", "config": {"cpu": 1, "memory": "2Gi", "minReplicas": 0, "maxReplicas": 5, "port": 80, "containerPort": 80, "envVariables": {"NODE_ENV": "production", "PORT": "80", "PUPPETEER_EXECUTABLE_PATH": "/usr/bin/chromium"}}}]}, "framework": {"name": "ai-resume", "plugins": {"function": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "./cloudfunctions"}}, "container": {"use": "@cloudbase/framework-plugin-container", "inputs": {"serviceName": "resume-snapshot", "servicePath": "./cloud-run/resume-snapshot", "containerPort": 80, "dockerfilePath": "./cloud-run/resume-snapshot/Dockerfile"}}, "mp": {"use": "@cloudbase/framework-plugin-mp", "inputs": {"appid": "", "privateKeyPath": "./private.key"}}}}}