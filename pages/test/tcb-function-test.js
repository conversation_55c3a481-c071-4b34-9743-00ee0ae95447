/**
 * TCB云函数真实测试页面
 * 在小程序环境中测试所有云函数
 */

const app = getApp();

Page({
  data: {
    testResults: {},
    isRunning: false,
    currentTest: '',
    logs: []
  },

  onLoad() {
    console.log('TCB函数测试页面加载');
  },

  /**
   * 开始端到端测试
   */
  async startE2ETest() {
    if (this.data.isRunning) return;

    this.setData({
      isRunning: true,
      testResults: {},
      logs: ['🚀 开始TCB云函数端到端测试...']
    });

    try {
      // 按顺序执行测试
      await this.testPingFunction();
      await this.testPdfProcessorWithOCR();
      await this.testResumeWorker();
      await this.testJdWorker();
      await this.testCvGenerator();

      this.addLog('✅ 所有测试完成');
      this.generateSummary();

    } catch (error) {
      this.addLog(`❌ 测试执行失败: ${error.message}`);
    } finally {
      this.setData({ isRunning: false, currentTest: '' });
    }
  },

  /**
   * 测试ping云函数
   */
  async testPingFunction() {
    this.setData({ currentTest: 'ping' });
    this.addLog('\n🏓 测试ping云函数...');

    try {
      const startTime = Date.now();

      const result = await wx.cloud.callFunction({
        name: 'ping',
        data: {
          message: 'health check',
          timestamp: new Date().toISOString()
        }
      });

      const responseTime = Date.now() - startTime;

      this.updateTestResult('ping', {
        status: 'success',
        responseTime: responseTime,
        result: result.result
      });

      this.addLog(`✅ ping测试成功 (${responseTime}ms)`);

    } catch (error) {
      this.updateTestResult('ping', {
        status: 'failed',
        error: error.message
      });
      this.addLog(`❌ ping测试失败: ${error.message}`);
    }
  },

  /**
   * 测试pdfProcessor云函数 - 使用真实PDF文件
   */
  async testPdfProcessorWithOCR() {
    this.setData({ currentTest: 'pdfProcessor' });
    this.addLog('\n📄 测试pdfProcessor云函数 - OCR功能...');

    try {
      const startTime = Date.now();

      // 1. 模拟PDF文件上传（使用base64编码的测试数据）
      this.addLog('📤 测试PDF上传...');

      // 这里使用一个简单的PDF测试数据
      const testPdfData = this.generateTestPdfData();

      const uploadResult = await wx.cloud.callFunction({
        name: 'pdfProcessor',
        data: {
          action: 'upload-pdf',
          data: {
            fileBuffer: testPdfData,
            fileName: '测试简历.pdf',
            userId: 'test-user-' + Date.now()
          }
        }
      });

      if (!uploadResult.result.success) {
        throw new Error('PDF上传失败');
      }

      this.addLog('✅ PDF上传成功');

      // 2. 测试OCR解析
      this.addLog('🔍 测试OCR解析...');

      const parseResult = await wx.cloud.callFunction({
        name: 'pdfProcessor',
        data: {
          action: 'parse-pdf',
          data: {
            fileId: uploadResult.result.data.fileId,
            fileName: '测试简历.pdf'
          }
        }
      });

      const responseTime = Date.now() - startTime;

      this.updateTestResult('pdfProcessor', {
        status: parseResult.result.success ? 'success' : 'failed',
        responseTime: responseTime,
        uploadResult: uploadResult.result.data,
        parseResult: parseResult.result.data
      });

      this.addLog(`✅ pdfProcessor测试完成 (${responseTime}ms)`);

      // 保存OCR结果供后续测试使用
      this.ocrText = parseResult.result.data.content;

    } catch (error) {
      this.updateTestResult('pdfProcessor', {
        status: 'failed',
        error: error.message
      });
      this.addLog(`❌ pdfProcessor测试失败: ${error.message}`);
    }
  },

  /**
   * 测试resumeWorker云函数
   */
  async testResumeWorker() {
    this.setData({ currentTest: 'resumeWorker' });
    this.addLog('\n👤 测试resumeWorker云函数...');

    try {
      const startTime = Date.now();

      // 使用OCR识别的文本或测试文本
      const resumeContent = this.ocrText || this.getTestResumeContent();

      const result = await wx.cloud.callFunction({
        name: 'resumeWorker',
        data: {
          resumeContent: resumeContent,
          fileName: '测试简历.pdf',
          fileType: 'pdf'
        }
      });

      const responseTime = Date.now() - startTime;

      this.updateTestResult('resumeWorker', {
        status: result.result.success ? 'success' : 'failed',
        responseTime: responseTime,
        result: result.result.data
      });

      this.addLog(`✅ resumeWorker测试完成 (${responseTime}ms)`);

      // 保存解析结果供后续测试使用
      this.resumeAnalysis = result.result.data;

    } catch (error) {
      this.updateTestResult('resumeWorker', {
        status: 'failed',
        error: error.message
      });
      this.addLog(`❌ resumeWorker测试失败: ${error.message}`);
    }
  },

  /**
   * 测试intelligentResumeGenerator云函数（JD分析功能）
   */
  async testJdWorker() {
    this.setData({ currentTest: 'jdWorker' });
    this.addLog('\n💼 测试intelligentResumeGenerator云函数（JD分析）...');

    try {
      const startTime = Date.now();

      const result = await wx.cloud.callFunction({
        name: 'intelligentResumeGenerator',
        data: {
          jdContent: this.getTestJdContent(),
          companyName: '测试公司',
          positionName: '智能客服产品运营专家',
          userBricks: [], // 空积木数组，仅进行JD分析
          personalInfo: {}, // 空个人信息
          templateId: 'default'
        }
      });

      const responseTime = Date.now() - startTime;

      // 提取JD分析结果
      const jdAnalysisData = result.result.success && result.result.data ?
        (result.result.data.jdAnalysis || result.result.data) : null;

      this.updateTestResult('jdWorker', {
        status: result.result.success ? 'success' : 'failed',
        responseTime: responseTime,
        result: jdAnalysisData
      });

      this.addLog(`✅ intelligentResumeGenerator JD分析测试完成 (${responseTime}ms)`);

      // 保存JD分析结果供后续测试使用
      this.jdAnalysis = jdAnalysisData;

    } catch (error) {
      this.updateTestResult('jdWorker', {
        status: 'failed',
        error: error.message
      });
      this.addLog(`❌ jdWorker测试失败: ${error.message}`);
    }
  },

  /**
   * 测试cvGenerator云函数
   */
  async testCvGenerator() {
    this.setData({ currentTest: 'cvGenerator' });
    this.addLog('\n📝 测试cvGenerator云函数...');

    try {
      const startTime = Date.now();

      // 需要JD分析和简历分析结果
      if (!this.jdAnalysis || !this.resumeAnalysis) {
        throw new Error('缺少JD分析或简历分析结果');
      }

      const result = await wx.cloud.callFunction({
        name: 'cvGenerator',
        data: {
          jdAnalysis: this.jdAnalysis,
          userBricks: this.resumeAnalysis.bricks || [],
          templateId: 'default',
          personalInfo: this.resumeAnalysis.personalInfo || {}
        }
      });

      const responseTime = Date.now() - startTime;

      this.updateTestResult('cvGenerator', {
        status: result.result.success ? 'success' : 'failed',
        responseTime: responseTime,
        result: result.result.data
      });

      this.addLog(`✅ cvGenerator测试完成 (${responseTime}ms)`);

    } catch (error) {
      this.updateTestResult('cvGenerator', {
        status: 'failed',
        error: error.message
      });
      this.addLog(`❌ cvGenerator测试失败: ${error.message}`);
    }
  },



  /**
   * 生成测试PDF数据
   */
  generateTestPdfData() {
    // 简单的PDF文件头（Base64编码）
    const pdfContent = `%PDF-1.4
1 0 obj
<< /Type /Catalog /Pages 2 0 R >>
endobj
2 0 obj
<< /Type /Pages /Kids [3 0 R] /Count 1 >>
endobj
3 0 obj
<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] >>
endobj
xref
0 4
********** 65535 f 
********** 00000 n 
********** 00000 n 
********** 00000 n 
trailer
<< /Size 4 /Root 1 0 R >>
startxref
174
%%EOF`;

    return Buffer.from(pdfContent).toString('base64');
  },

  /**
   * 获取测试简历内容
   */
  getTestResumeContent() {
    return `徐瑜泽 (Ian Tsui)
Account Manager | 国际电商 & 客户成功
联系方式: 139-2830-3116 | <EMAIL>
语言: 英语 / 粤语 / 客家话 / 普通话
所在地: 上海，中国

专业概览 (SUMMARY)
拥有超过 5 年电商平台商家运营与增长策略经验的专家，专注于驱动全链路商业增长。在亚马逊期间，成功管理超过 8 万名跨境卖家，通过数据驱动的策略将名下卖家总 GMV 提升至 1 亿美元。

工作经历 (EXPERIENCE)
亚马逊 (Amazon) | 上海
商家运营 (Account Manager) | 2022 年 5 月 ‒ 2025 年 5 月
• 核心业务增长驱动：全面负责超过 8 万名跨境卖家的全生命周期管理
• 运营体系搭建与推广：从 0 到 1 搭建并推广三级客户管理体系`;
  },

  /**
   * 获取测试JD内容
   */
  getTestJdContent() {
    return `智能客服产品运营专家
北京
正式
运营 - 产品运营

职位描述
1、理解和熟悉算法运作原理，基于业务现状，进行问题分类并设计该类问题场景的业务解决方案；
2、通过监控渠道的运营状况，对业务和服务变化进行及时的服务策略设计和部署；
3、负责智能知识库体系建设，优化机器人解决方案的表达形式、表达文案、对话流程设计；
4、负责分析智能侧服务需求，优化业务流程和产品功能，提升智能服务能力；

职位要求
1、3年以上智能客服工作经验；
2、对智能服务和NLP领域有较高的热情和好奇心；
3、能够利用AI能力为人工提效，具有一定的智能服务领域前瞻性；`;
  },

  /**
   * 更新测试结果
   */
  updateTestResult(functionName, result) {
    const testResults = this.data.testResults;
    testResults[functionName] = result;
    this.setData({ testResults });
  },

  /**
   * 添加日志
   */
  addLog(message) {
    const logs = this.data.logs;
    logs.push(message);
    this.setData({ logs });
    console.log(message);
  },

  /**
   * 生成测试摘要
   */
  generateSummary() {
    const results = this.data.testResults;
    const functionNames = Object.keys(results);
    const successCount = functionNames.filter(name => results[name].status === 'success').length;
    const totalCount = functionNames.length;
    const successRate = ((successCount / totalCount) * 100).toFixed(1);

    this.addLog('\n📊 测试摘要');
    this.addLog(`总计: ${totalCount} 个函数`);
    this.addLog(`成功: ${successCount} 个`);
    this.addLog(`失败: ${totalCount - successCount} 个`);
    this.addLog(`成功率: ${successRate}%`);

    // 显示详细结果
    functionNames.forEach(name => {
      const result = results[name];
      const status = result.status === 'success' ? '✅' : '❌';
      const time = result.responseTime ? ` (${result.responseTime}ms)` : '';
      this.addLog(`${status} ${name}${time}`);
    });
  },

  /**
   * 清除测试结果
   */
  clearResults() {
    this.setData({
      testResults: {},
      logs: [],
      currentTest: ''
    });
  }
});
