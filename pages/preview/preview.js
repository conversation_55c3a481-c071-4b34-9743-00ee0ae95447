// 从全局获取ApiService，兼容微信小程序
const ApiService = global.ApiService || getApp().ApiService

Page({
  data: {
    // 预览数据
    previewData: null,
    userName: '',
    companyName: '',
    positionName: '',
    jdContent: '',

    // 简历数据
    resumeData: null,
    jdAnalysisData: null,
    resumeId: '',
    generatedAt: '',

    // PDF相关信息
    pdfUrl: null,
    fileID: null,
    cloudPath: null,

    // 简历预览
    resumePreview: '',
    isGeneratingPreview: false,

    // PNG图片预览相关
    previewMode: 'image', // 'image' 或 'text'
    resumeImageUrl: null,
    resumeHtmlContent: null, // 新增：存储HTML内容
    isGeneratingImage: false,
    imageError: false,
    estimatedTime: 25,

    // 模板和样式
    templates: ['标准模板', '创意模板', '简约模板'],
    styles: ['专业', '现代', '经典'],
    selectedTemplate: 0,
    selectedStyle: 0,

    // 状态控制
    loading: false,
    loadingText: '加载中...',
    useMockData: false,
    dataType: 'generated' // 'generated' 或 'manual'
  },

  onLoad(options) {
    console.log('简历预览页面加载', options)

    // 解析传递的数据
    if (options.data) {
      try {
        const previewData = JSON.parse(decodeURIComponent(options.data))
        this.setData({ previewData })
        this.initializePreviewData(previewData)
        console.log('接收到的预览数据:', previewData)
      } catch (error) {
        console.error('解析传递数据失败:', error)
      }
    }

    this.checkCloudEnvironment()
  },

  onShow() {
    console.log('简历预览页面显示')
  },

  // 检查云开发环境
  checkCloudEnvironment() {
    const envId = wx.getAccountInfoSync().miniProgram.envVersion
    if (envId === 'develop' || envId === 'trial') {
      this.setData({ useMockData: true })
    }
  },

  // 初始化预览数据 - 支持真实AI生成数据
  initializePreviewData(data) {
    console.log('🔄 初始化预览数据:', data)

    // 检查数据类型：AI生成 vs 手动选择
    if ((data.type === 'generated' || data.type === 'unified') && data.resume) {
      // AI生成的简历数据（支持统一架构和传统架构）
      console.log('✅ 检测到真实AI生成数据，使用真实内容')
      this.initializeGeneratedResume(data)
    } else {
      // 传统的手动选择数据或缺少AI生成数据
      console.log('⚠️ 未检测到AI生成数据，使用基础预览模式')
      this.initializeManualResume(data)
    }
  },

  // 初始化AI生成的简历数据
  initializeGeneratedResume(data) {
    console.log('🤖 初始化AI生成的简历数据')

    const resumeData = data.resume
    const jdAnalysis = data.jdAnalysis

    // 设置基本信息
    this.setData({
      userName: resumeData?.personalInfo?.name || '求职者',
      companyName: data.companyName,
      positionName: data.positionName,
      jdContent: data.jdContent,
      previewData: data,
      resumeId: data.resumeId,
      generatedAt: data.generatedAt,

      // 设置真实的简历内容 - 修复显示问题
      resumePreview: this.formatGeneratedResumeContent(resumeData),

      // 设置JD分析结果
      jdAnalysisResult: jdAnalysis,
      jdAnalysisData: jdAnalysis,

      // 暂存简历数据，不立即生成PDF
      tempResumeData: resumeData,
      tempJdAnalysis: jdAnalysis,
      resumeData: resumeData, // 保存完整的简历数据

      // PDF相关信息 - 暂时清空，等用户点击保存时再生成
      pdfUrl: null,
      fileID: null,
      cloudPath: null,

      // 标记为真实数据
      isRealData: true,
      useMockData: false,
      dataType: 'generated'
    })

    console.log('✅ AI生成简历数据初始化完成')
    console.log('📄 简历内容已暂存，等待用户保存PDF')
  },

  // 格式化生成的简历内容 - 增强版处理CVGenerator返回的数据
  formatGeneratedResumeContent(resumeData) {
    console.log('🎨 开始格式化生成的简历内容')
    console.log('📊 输入数据类型:', typeof resumeData)
    console.log('📊 输入数据是否为空:', !resumeData)

    if (!resumeData) {
      console.warn('⚠️ 简历数据为空，返回默认提示')
      return '简历内容生成失败，请重新生成'
    }

    try {
      // 详细记录数据结构用于调试
      console.log('🔍 详细数据分析:')
      console.log('- 数据类型:', typeof resumeData)
      console.log('- 是否为数组:', Array.isArray(resumeData))
      console.log('- 数据键值:', Object.keys(resumeData || {}))

      // 安全地记录数据内容（避免循环引用）
      try {
        const dataPreview = JSON.stringify(resumeData, null, 2).substring(0, 500)
        console.log('- 数据预览:', dataPreview + (dataPreview.length >= 500 ? '...' : ''))
      } catch (jsonError) {
        console.log('- 数据包含循环引用，无法完整序列化')
      }

      // 如果resumeData是字符串，直接返回
      if (typeof resumeData === 'string') {
        console.log('✅ 检测到字符串格式，直接返回')
        return resumeData.trim() || '简历内容为空'
      }

      // 检查是否有resumeContent字段（CVGenerator返回的格式）
      if (resumeData && resumeData.resumeContent) {
        console.log('✅ 检测到resumeContent字段')
        if (typeof resumeData.resumeContent === 'string') {
          console.log('✅ resumeContent是字符串，直接返回')
          return resumeData.resumeContent.trim() || '简历内容为空'
        }
        // 如果resumeContent是对象，格式化它
        console.log('🔄 resumeContent是对象，进行格式化')
        return this.formatResumeObject(resumeData.resumeContent)
      }

      // 检查是否有content字段
      if (resumeData && resumeData.content) {
        console.log('✅ 检测到content字段')
        if (typeof resumeData.content === 'string') {
          console.log('✅ content是字符串，直接返回')
          return resumeData.content.trim() || '简历内容为空'
        }
        console.log('🔄 content是对象，进行格式化')
        return this.formatResumeObject(resumeData.content)
      }

      // 检查是否有data字段
      if (resumeData && resumeData.data) {
        console.log('✅ 检测到data字段')
        if (typeof resumeData.data === 'string') {
          console.log('✅ data是字符串，直接返回')
          return resumeData.data.trim() || '简历内容为空'
        }
        console.log('🔄 data是对象，进行格式化')
        return this.formatResumeObject(resumeData.data)
      }

      // 如果是直接的简历对象，格式化为可读的简历格式
      console.log('🔄 作为简历对象进行格式化')
      return this.formatResumeObject(resumeData)

    } catch (error) {
      console.error('❌ 格式化生成简历内容失败:', error)
      console.error('错误详情:', error.message)
      console.error('错误堆栈:', error.stack)
      console.error('数据类型:', typeof resumeData)

      // 尝试提供更有用的错误信息
      let errorDetails = ''
      if (resumeData) {
        try {
          errorDetails = `数据键值: ${Object.keys(resumeData).join(', ')}`
        } catch (e) {
          errorDetails = '无法获取数据键值'
        }
      }

      return `简历内容格式化失败，请重新生成。\n\n调试信息：\n- 错误: ${error.message}\n- ${errorDetails}`
    }
  },

  // 格式化简历对象为可读文本 - 增强版
  formatResumeObject(resumeObj) {
    console.log('📝 开始格式化简历对象')
    console.log('📊 对象类型:', typeof resumeObj)
    console.log('📊 对象是否为空:', !resumeObj)

    if (!resumeObj) {
      console.warn('⚠️ 简历对象为空')
      return '简历数据为空'
    }

    let formattedResume = ''

    try {
      console.log('🔍 分析简历对象结构:')
      console.log('- 可用键值:', Object.keys(resumeObj))

      // 个人信息
      if (resumeObj.personalInfo) {
        console.log('✅ 处理个人信息')
        const info = resumeObj.personalInfo
        formattedResume += `${info.name || '求职者'}\n`
        if (info.title) {
          formattedResume += `${info.title}\n`
        }
        if (info.phone || info.email) {
          formattedResume += `${info.phone || ''} | ${info.email || ''}\n`
        }
        if (info.location) {
          formattedResume += `${info.location}\n`
        }
        formattedResume += '\n'
      }

      // 专业概览/个人简介
      if (resumeObj.summary || resumeObj.professionalSummary) {
        console.log('✅ 处理专业概览')
        formattedResume += `【专业概览】\n${resumeObj.summary || resumeObj.professionalSummary}\n\n`
      }

      // 核心技能 - 增强版处理多种技能格式
      const skills = resumeObj.skills || resumeObj.coreSkills
      if (skills) {
        console.log('✅ 处理核心技能')
        formattedResume += `【核心技能】\n`

        if (Array.isArray(skills)) {
          // 如果是数组
          console.log('- 技能格式: 数组')
          skills.forEach(skill => {
            formattedResume += `• ${skill}\n`
          })
        } else if (typeof skills === 'object') {
          // 如果是对象，处理technical, soft等分类
          console.log('- 技能格式: 对象')
          if (skills.technical && Array.isArray(skills.technical)) {
            formattedResume += `技术技能：\n`
            skills.technical.forEach(skill => {
              formattedResume += `• ${skill}\n`
            })
          }
          if (skills.soft && Array.isArray(skills.soft)) {
            formattedResume += `软技能：\n`
            skills.soft.forEach(skill => {
              formattedResume += `• ${skill}\n`
            })
          }
          if (skills.languages && Array.isArray(skills.languages)) {
            formattedResume += `语言能力：\n`
            skills.languages.forEach(lang => {
              formattedResume += `• ${lang}\n`
            })
          }
        } else if (typeof skills === 'string') {
          // 如果是字符串
          console.log('- 技能格式: 字符串')
          formattedResume += `${skills}\n`
        }
        formattedResume += '\n'
      }

      // 工作经验 - 增强版处理多种格式
      const experience = resumeObj.experience || resumeObj.workExperience
      if (experience && Array.isArray(experience) && experience.length > 0) {
        console.log('✅ 处理工作经验')
        formattedResume += `【工作经验】\n`
        experience.forEach(exp => {
          formattedResume += `${exp.title || exp.position || '职位'} | ${exp.company || '公司'}`
          if (exp.duration) formattedResume += ` | ${exp.duration}`
          formattedResume += '\n'

          // 处理职责描述
          if (exp.description) {
            formattedResume += `${exp.description}\n`
          } else if (exp.responsibilities) {
            if (Array.isArray(exp.responsibilities)) {
              exp.responsibilities.forEach(resp => {
                formattedResume += `• ${resp}\n`
              })
            } else {
              formattedResume += `${exp.responsibilities}\n`
            }
          }

          // 处理成就
          if (exp.achievements && Array.isArray(exp.achievements)) {
            formattedResume += `主要成就：\n`
            exp.achievements.forEach(achievement => {
              formattedResume += `• ${achievement}\n`
            })
          }

          formattedResume += '\n'
        })
      }

      // 项目经验
      const projects = resumeObj.projects || resumeObj.projectExperience
      if (projects && Array.isArray(projects) && projects.length > 0) {
        console.log('✅ 处理项目经验')
        formattedResume += `【项目经验】\n`
        projects.forEach(project => {
          formattedResume += `${project.name || '项目'}`
          if (project.role) formattedResume += ` | ${project.role}`
          formattedResume += '\n'
          if (project.description) {
            formattedResume += `${project.description}\n`
          }
          formattedResume += '\n'
        })
      }

      // 教育背景
      if (resumeObj.education && Array.isArray(resumeObj.education) && resumeObj.education.length > 0) {
        console.log('✅ 处理教育背景')
        formattedResume += `【教育背景】\n`
        resumeObj.education.forEach(edu => {
          formattedResume += `${edu.school || '学校'} | ${edu.degree || '学位'}\n`
          if (edu.major) formattedResume += `专业：${edu.major}\n`
          if (edu.graduationYear) formattedResume += `毕业年份：${edu.graduationYear}\n`
          formattedResume += '\n'
        })
      }

      // 如果没有格式化出任何内容，尝试直接显示对象的字符串表示
      if (!formattedResume.trim()) {
        console.log('⚠️ 没有识别到标准简历格式，尝试通用格式化')
        formattedResume = this.formatGenericObject(resumeObj)
      }

      const result = formattedResume || '简历内容格式化失败'
      console.log('✅ 简历对象格式化完成，内容长度:', result.length)
      return result

    } catch (error) {
      console.error('❌ 格式化简历对象失败:', error)
      console.error('错误详情:', error.message)
      console.error('错误堆栈:', error.stack)
      console.error('数据类型:', typeof resumeObj)

      // 尝试提供备用格式化
      try {
        console.log('🔄 尝试备用格式化方案')
        return this.formatGenericObject(resumeObj)
      } catch (backupError) {
        console.error('❌ 备用格式化也失败:', backupError)
        return `简历内容格式化失败：${error.message}\n\n请检查数据格式或联系技术支持。`
      }
    }
  },

  // 通用对象格式化 - 作为备用方案
  formatGenericObject(obj) {
    console.log('🔄 使用通用对象格式化')

    if (!obj) return '数据为空'

    if (typeof obj === 'string') return obj

    let result = ''

    try {
      // 遍历对象的所有属性
      Object.keys(obj).forEach(key => {
        const value = obj[key]

        if (value && typeof value === 'string' && value.trim()) {
          result += `【${key}】\n${value}\n\n`
        } else if (Array.isArray(value) && value.length > 0) {
          result += `【${key}】\n`
          value.forEach(item => {
            if (typeof item === 'string') {
              result += `• ${item}\n`
            } else if (typeof item === 'object' && item) {
              result += `• ${JSON.stringify(item)}\n`
            }
          })
          result += '\n'
        } else if (typeof value === 'object' && value) {
          result += `【${key}】\n${JSON.stringify(value, null, 2)}\n\n`
        }
      })

      return result || '无法解析的数据格式'

    } catch (error) {
      console.error('❌ 通用格式化失败:', error)
      return `数据格式化失败: ${error.message}`
    }
  },

  // 格式化真实的简历内容 - 保留原有函数
  formatRealResumeContent(resumeData) {
    if (!resumeData) {
      return '简历内容生成失败，请重新生成'
    }

    try {
      // 如果resumeData是字符串，直接返回
      if (typeof resumeData === 'string') {
        return resumeData
      }

      // 如果是对象，格式化为可读的简历格式
      return this.formatResumeObject(resumeData)

    } catch (error) {
      console.error('❌ 格式化简历内容失败:', error)
      return '简历内容格式化失败，请重新生成'
    }
  },



  // 初始化手动选择的简历  
  initializeManualResume(data) {
    console.log('👤 处理手动选择的简历数据')

    const { analysis, jdAnalysis, companyName, positionName, userName, jdContent } = data

    this.setData({
      userName: userName || '求职者',
      companyName: companyName || '目标公司',
      positionName: positionName || '目标职位',
      jdContent: jdContent || '',
      jdAnalysisData: jdAnalysis || null,
      dataType: 'manual'
    })

    // 生成传统的简历预览
    this.generateResumePreview()
  },

  // 生成AI简历预览
  generateAIResumePreview(resume) {
    console.log('🎨 生成AI简历预览')

    this.setData({ isGeneratingPreview: true })

    try {
      let content = this.formatAIResumeContent(resume)

      this.setData({
        resumePreview: content,
        isGeneratingPreview: false
      })

      console.log('✅ AI简历预览生成完成')
    } catch (error) {
      console.error('❌ 生成AI简历预览失败:', error)
      this.setData({
        isGeneratingPreview: false,
        resumePreview: '简历预览生成失败，请重试'
      })
    }
  },

  // 格式化AI简历内容
  formatAIResumeContent(resume) {
    if (!resume) return '简历数据不完整'

    let content = ''

    // 个人信息
    const personalInfo = resume.personalInfo || {}
    content += `${personalInfo.name || '求职者'}\n`

    if (personalInfo.phone || personalInfo.email) {
      content += `${personalInfo.phone || ''} | ${personalInfo.email || ''}\n`
    }

    if (personalInfo.location) {
      content += `${personalInfo.location}\n`
    }

    content += '\n'

    // 专业概览
    if (resume.professionalSummary) {
      content += `【专业概览】\n${resume.professionalSummary}\n\n`
    }

    // 核心技能
    if (resume.coreSkills && resume.coreSkills.length > 0) {
      content += `【核心技能】\n`
      resume.coreSkills.forEach(skill => {
        content += `• ${skill}\n`
      })
      content += '\n'
    }

    // 工作经历
    if (resume.workExperience && resume.workExperience.length > 0) {
      content += `【工作经历】\n`
      resume.workExperience.forEach(work => {
        content += `${work.company || '公司'} | ${work.position || '职位'}\n`
        if (work.duration) content += `${work.duration}\n`
        if (work.responsibilities) {
          content += `职责：\n${work.responsibilities}\n`
        }
        content += '\n'
      })
    }

    // 项目经历
    if (resume.projectExperience && resume.projectExperience.length > 0) {
      content += `【项目经历】\n`
      resume.projectExperience.forEach(project => {
        content += `${project.name || '项目'} | ${project.role || ''}\n`
        if (project.duration) content += `${project.duration}\n`
        if (project.description) {
          content += `描述：\n${project.description}\n`
        }
        content += '\n'
      })
    }

    // 教育背景
    if (resume.education && resume.education.length > 0) {
      content += `【教育背景】\n`
      resume.education.forEach(edu => {
        content += `${edu.school || '学校'} | ${edu.degree || '学位'}\n`
        if (edu.major) content += `专业：${edu.major}\n`
        if (edu.graduationYear) content += `毕业年份：${edu.graduationYear}\n`
        content += '\n'
      })
    }

    return content
  },

  // 生成简历预览 - 使用真实数据或调用真实API
  async generateResumePreview() {
    console.log('🎨 生成简历预览')

    this.setData({ isGeneratingPreview: true })

    try {
      // 检查是否已有真实的简历数据
      if (this.data.isRealData && this.data.resumePreview) {
        console.log('✅ 使用已有的真实简历数据')
        this.setData({
          isGeneratingPreview: false
        })
        return
      }

      // 如果没有真实数据，尝试重新生成
      console.log('🔄 没有真实数据，尝试重新生成简历')

      // 修复：确保在微信环境中正确获取HttpApiService
      try {
        console.log('📡 直接调用云函数生成简历预览');

        // 直接调用intelligentResumeGenerator进行一体化生成
        console.log('📡 调用intelligentResumeGenerator进行一体化简历生成');
        const cloudResult = await wx.cloud.callFunction({
          name: 'intelligentResumeGenerator',
          data: {
            jdContent: this.data.jdContent,
            companyName: this.data.companyName,
            positionName: this.data.positionName,
            userBricks: this.data.userBricks || [],
            userInfo: this.data.userInfo || {},
            templateId: 'default'
          }
        });

        console.log('📡 云函数调用结果:', cloudResult);

        // 处理云函数返回结果
        let result;
        if (cloudResult.result) {
          // 检查是否是HTTP响应格式
          if (cloudResult.result.statusCode !== undefined) {
            if (cloudResult.result.statusCode === 200) {
              const responseData = JSON.parse(cloudResult.result.body);
              result = responseData;
            } else {
              // 处理错误响应
              const errorData = JSON.parse(cloudResult.result.body);
              throw new Error(errorData.error || '云函数执行失败');
            }
          } else {
            // 直接返回格式
            result = cloudResult.result;
          }
        } else {
          throw new Error('云函数调用失败');
        }

        // 检查云函数调用结果
        if (result && result.success) {
          console.log('✅ 云函数调用成功，处理返回数据');

          // intelligentResumeGenerator返回的数据结构：result.data.data.resume
          const resumeData = result.data?.data?.resume || result.data;

          // 更新页面数据
          this.setData({
            resumePreview: this.formatGeneratedResumeContent(resumeData),
            tempResumeData: resumeData,
            isGeneratingPreview: false,
            isRealData: true
          });

          console.log('✅ 简历预览生成成功');
          return;
        } else {
          throw new Error(result?.error || '云函数返回数据格式错误');
        }

      } catch (error) {
        console.error('❌ 云函数调用失败:', error);
        throw new Error('云函数调用失败: ' + error.message);
      }

    } catch (error) {
      console.error('❌ 生成简历预览失败:', error)

      // 作为最后的备用方案，显示基础信息
      const basicPreview = this.generateBasicPreview()

      this.setData({
        isGeneratingPreview: false,
        resumePreview: basicPreview,
        isRealData: false
      })

      wx.showToast({
        title: '预览生成失败，显示基础信息',
        icon: 'none',
        duration: 3000
      })
    }
  },

  // 生成基础预览（仅在API失败时使用）
  generateBasicPreview() {
    const { userName, companyName, positionName } = this.data
    const userInfo = this.data.previewData?.userInfo

    return `${userName || userInfo?.nickName || '求职者'}

目标职位：${positionName || '待定'}
目标公司：${companyName || '待定'}

【基础信息】
此简历正在生成中，请稍后刷新查看完整内容。

【说明】
由于网络或服务问题，无法显示完整的AI生成简历。
建议返回重新生成或检查网络连接。

【调试信息】
- 数据类型：${this.data.previewData?.type || '未知'}
- 生成时间：${this.data.previewData?.generatedAt || '未知'}
- 是否有JD分析：${this.data.previewData?.jdAnalysis ? '是' : '否'}
- 是否有简历数据：${this.data.previewData?.resume ? '是' : '否'}`
  },

  // 选择模板
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index
    this.setData({ selectedTemplate: index })
    console.log('选择模板:', this.data.templates[index])
    // 更新预览效果
    this.updatePreviewStyle()
  },

  // 选择风格
  selectStyle(e) {
    const index = e.currentTarget.dataset.index
    this.setData({ selectedStyle: index })
    console.log('选择风格:', this.data.styles[index])
    // 更新预览效果
    this.updatePreviewStyle()
  },

  // 更新预览样式
  updatePreviewStyle() {
    // 在实际应用中，这里应该根据选择的模板和风格更新预览样式
    // 这里仅做示例，实际效果需要根据需求实现
    console.log('更新预览样式:', {
      template: this.data.templates[this.data.selectedTemplate],
      style: this.data.styles[this.data.selectedStyle]
    })
  },

  // 生成最终简历
  generateFinalResume() {
    console.log('🚀 生成最终简历')

    this.setData({
      loading: true,
      loadingText: '正在生成最终简历...'
    })

    // 在实际应用中，这里应该调用API生成最终简历
    // 这里使用模拟数据
    setTimeout(() => {
      this.setData({ loading: false })

      wx.showModal({
        title: '简历生成成功',
        content: '您的简历已生成成功！',
        confirmText: '查看',
        success: (res) => {
          if (res.confirm) {
            // 跳转到编辑页面
            wx.navigateTo({
              url: '/pages/edit/edit?resumeId=' + (this.data.resumeId || 'mock_resume_id')
            })
          } else {
            // 返回首页
            wx.switchTab({
              url: '/pages/upload/upload'
            })
          }
        }
      })
    }, 1500)
  },

  // 保存PDF简历 - 修改为生成并保存PDF
  async downloadPDF() {
    console.log('🚀 开始生成并保存PDF简历');

    // 检查是否有暂存的简历数据
    if (!this.data.tempResumeData && !this.data.resumeData) {
      wx.showToast({
        title: '没有可以保存的简历内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.showLoading({
      title: '正在生成PDF...',
    });

    try {
      // 调用CVGenerator生成PDF
      const pdfResult = await this.generatePDFFromResumeData();

      if (!pdfResult.success) {
        throw new Error(pdfResult.error || 'PDF生成失败');
      }

      // 更新PDF信息
      this.setData({
        pdfUrl: pdfResult.data.pdfUrl,
        fileID: pdfResult.data.fileID,
        cloudPath: pdfResult.data.cloudPath
      });

      wx.hideLoading();

      // 下载并打开PDF
      await this.downloadAndOpenPDF(pdfResult.data.pdfUrl, pdfResult.data.isLocalFile);

      wx.showToast({
        title: 'PDF简历已保存',
        icon: 'success',
        duration: 2000
      });

    } catch (err) {
      wx.hideLoading();
      console.error('❌ 生成PDF失败:', err);

      wx.showToast({
        title: err.message || 'PDF生成失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 从简历数据生成PDF - 微信环境优化版
  async generatePDFFromResumeData() {
    try {
      // 修复：确保在微信环境中正确获取HttpApiService
      let HttpApiService;
      try {
        HttpApiService = global.HttpApiService || getApp().HttpApiService;
        if (!HttpApiService) {
          console.warn('⚠️ HttpApiService不可用，使用直接请求方案');
          // 备用方案：直接使用wx.request调用SCF函数
          HttpApiService = {
            requestSCF: async (functionName, endpoint, options) => {
              const scfUrls = {
                cvGenerator: 'http://1341667342-jcyribeze9.ap-guangzhou.tencentscf.com'
              };

              return new Promise((resolve, reject) => {
                wx.request({
                  url: scfUrls[functionName],
                  method: 'POST',
                  data: options.data,
                  timeout: 90000,
                  success: (response) => {
                    if (response.statusCode === 200) {
                      resolve(response.data);
                    } else {
                      reject(new Error(`请求失败: ${response.statusCode}`));
                    }
                  },
                  fail: reject
                });
              });
            }
          };
        }
      } catch (error) {
        console.error('❌ 获取HttpApiService失败:', error);
        throw new Error('HttpApiService模块加载失败');
      }

      // 使用可用的简历数据
      const resumeData = this.data.tempResumeData || this.data.resumeData;
      const jdAnalysis = this.data.tempJdAnalysis || this.data.jdAnalysisData;

      // 确保数据完整性和JSON序列化安全
      const pdfRequestData = {
        jdAnalysis: jdAnalysis || {},
        userBricks: resumeData || {},
        resumeData: resumeData || {},
        companyName: this.data.companyName || '目标公司',
        positionName: this.data.positionName || '目标职位',
        userInfo: this.data.previewData?.userInfo || {},
        action: 'generate-pdf',
        timestamp: new Date().toISOString()
      };

      console.log('📡 调用CVGenerator生成PDF:', {
        ...pdfRequestData,
        userBricks: '数据已包含',
        resumeData: '数据已包含'
      });

      const result = await HttpApiService.requestSCF('cvGenerator', '', {
        method: 'POST',
        data: pdfRequestData
      });

      if (result.success && result.data && result.data.pdfBase64) {
        // 将base64 PDF保存到云存储
        const cloudResult = await this.savePDFToCloud(result.data.pdfBase64);

        return {
          success: true,
          data: {
            pdfUrl: cloudResult.pdfUrl,
            fileID: cloudResult.fileID,
            cloudPath: cloudResult.cloudPath,
            pdfBase64: result.data.pdfBase64
          }
        };
      }

      return result;

    } catch (error) {
      console.error('❌ 调用CVGenerator生成PDF失败:', error);
      return {
        success: false,
        error: error.message || 'PDF生成服务调用失败'
      };
    }
  },

  // 将PDF保存到云存储
  async savePDFToCloud(pdfBase64) {
    try {
      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `resume_${timestamp}.pdf`;
      const cloudPath = `resumes-out/${fileName}`;

      // 先创建本地临时文件
      const fs = wx.getFileSystemManager();
      const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_upload_${timestamp}.pdf`;

      // 写入临时文件
      await new Promise((resolve, reject) => {
        fs.writeFile({
          filePath: tempFilePath,
          data: pdfBase64,
          encoding: 'base64',
          success: resolve,
          fail: reject
        });
      });

      // 上传到云存储
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: tempFilePath
      });

      // 删除临时文件
      try {
        fs.unlinkSync(tempFilePath);
      } catch (e) {
        console.log('删除临时文件失败:', e);
      }

      if (uploadResult.fileID) {
        // 获取临时下载链接
        const tempUrlResult = await wx.cloud.getTempFileURL({
          fileList: [uploadResult.fileID]
        });

        if (tempUrlResult.fileList && tempUrlResult.fileList[0]) {
          return {
            pdfUrl: tempUrlResult.fileList[0].tempFileURL,
            fileID: uploadResult.fileID,
            cloudPath: cloudPath
          };
        }
      }

      throw new Error('云存储上传失败');

    } catch (error) {
      console.error('❌ 保存PDF到云存储失败:', error);

      // 如果云存储失败，创建本地临时文件
      return this.createLocalTempPDF(pdfBase64);
    }
  },

  // 创建本地临时PDF文件
  async createLocalTempPDF(pdfBase64) {
    try {
      // 将base64转换为临时文件
      const fs = wx.getFileSystemManager();
      const tempFilePath = `${wx.env.USER_DATA_PATH}/temp_resume.pdf`;

      // 写入文件
      await new Promise((resolve, reject) => {
        fs.writeFile({
          filePath: tempFilePath,
          data: pdfBase64,
          encoding: 'base64',
          success: resolve,
          fail: reject
        });
      });

      return {
        pdfUrl: tempFilePath,
        fileID: null,
        cloudPath: tempFilePath,
        isLocalFile: true
      };

    } catch (error) {
      console.error('❌ 创建本地临时PDF失败:', error);
      throw new Error('PDF文件创建失败');
    }
  },

  // 下载并打开PDF文件
  async downloadAndOpenPDF(pdfUrl, isLocalFile = false) {
    if (!pdfUrl) {
      throw new Error('PDF URL为空');
    }

    console.log('📄 处理PDF文件:', pdfUrl, '本地文件:', isLocalFile);

    let filePath = pdfUrl;

    // 如果不是本地文件，需要下载
    if (!isLocalFile) {
      // 使用 wx.downloadFile 下载文件
      const res = await new Promise((resolve, reject) => {
        wx.downloadFile({
          url: pdfUrl,
          success: (response) => {
            if (response.statusCode === 200) {
              resolve(response);
            } else {
              reject({ errMsg: `服务器返回错误, 状态码: ${response.statusCode}` });
            }
          },
          fail: (err) => reject(err),
        });
      });

      console.log('✅ 文件下载成功，临时路径:', res.tempFilePath);
      filePath = res.tempFilePath;
    }

    // 使用 wx.openDocument 打开文件
    wx.openDocument({
      filePath: filePath,
      showMenu: true, // 在iOS上显示右上角菜单，允许用户转发或另存为
      success: () => {
        console.log('📄 PDF文件打开成功');
      },
      fail: (err) => {
        console.error('❌ 打开PDF失败:', err);
        wx.showToast({
          title: '打开文件失败: ' + (err.errMsg || '未知错误'),
          icon: 'none',
        });
      }
    });
  },

  // 返回上一页
  goBack() {
    console.log('点击返回修改按钮')

    // 获取页面栈
    const pages = getCurrentPages()
    console.log('当前页面栈:', pages.length)

    if (pages.length > 1) {
      // 如果有上一页，则返回
      wx.navigateBack({
        delta: 1,
        success: () => {
          console.log('返回上一页成功')
        },
        fail: (err) => {
          console.error('返回失败:', err)
          this.redirectToGenerate()
        }
      })
    } else {
      // 如果是第一页，则重定向到简历生成页面
      this.redirectToGenerate()
    }
  },

  // 重定向到简历生成页面
  redirectToGenerate() {
    console.log('重定向到简历生成页面')

    // 准备传递的数据
    let redirectData = {}

    if (this.data.previewData) {
      // 如果有预览数据，则传递给简历生成页面
      redirectData = {
        jdContent: this.data.previewData.jdContent || '',
        companyName: this.data.previewData.companyName || '',
        positionName: this.data.previewData.positionName || '',
        analysis: this.data.previewData.jdAnalysis ?
          encodeURIComponent(JSON.stringify(this.data.previewData.jdAnalysis)) : ''
      }
    }

    // 构建URL查询参数
    const queryParams = Object.keys(redirectData)
      .filter(key => redirectData[key])
      .map(key => `${key}=${redirectData[key]}`)
      .join('&')

    const url = `/pages/generate/generate${queryParams ? '?' + queryParams : ''}`

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('重定向到简历生成页面成功')
      },
      fail: (err) => {
        console.error('重定向失败:', err)

        // 最后尝试使用switchTab
        wx.switchTab({
          url: '/pages/generate/generate'
        })
      }
    })
  },

  // 切换预览模式
  switchPreviewMode(e) {
    const mode = e.currentTarget.dataset.mode;
    console.log('切换预览模式:', mode);

    this.setData({ previewMode: mode });

    // 如果切换到图片模式且还没有图片，自动生成
    if (mode === 'image' && !this.data.resumeImageUrl && !this.data.isGeneratingImage) {
      this.generateResumeImage();
    }

    // 如果切换到文本模式且还没有HTML内容，自动生成
    if (mode === 'text' && !this.data.resumeHtmlContent && !this.data.isGeneratingImage) {
      this.generateResumeImage();
    }
  },

  // 生成简历PNG图片
  async generateResumeImage() {
    console.log('🖼️ 开始生成简历PNG图片');

    // 检查是否有简历数据
    if (!this.data.resumeData && !this.data.tempResumeData) {
      wx.showToast({
        title: '没有可用的简历数据',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({
      isGeneratingImage: true,
      imageError: false,
      estimatedTime: 25
    });

    // 倒计时显示
    this.startCountdown();

    try {
      // 准备简历数据
      const resumeData = this.data.tempResumeData || this.data.resumeData;
      const templateType = this.getSelectedTemplate();

      console.log('📡 调用resumePreviewGenerator云函数');

      // 调用云函数生成PNG
      const result = await wx.cloud.callFunction({
        name: 'resumePreviewGenerator',
        data: {
          resumeData: resumeData,
          format: 'png' // 明确指定生成PNG格式
        }
      });

      console.log('📡 云函数调用结果:', result);

      if (result.result.statusCode === 200) {
        const responseData = JSON.parse(result.result.body);

        if (responseData.success && responseData.data) {
          // 检查PNG数据结构
          const pngData = responseData.data.png;
          const htmlData = responseData.data.html;

          if (pngData && pngData.imageUrl) {
            this.setData({
              resumeImageUrl: pngData.imageUrl,
              resumeHtmlContent: htmlData?.content || htmlData?.url, // 保存HTML内容或URL
              isGeneratingImage: false,
              imageError: false
            });

            wx.showToast({
              title: '图片生成成功',
              icon: 'success',
              duration: 2000
            });

            console.log('✅ PNG图片生成成功:', pngData.imageUrl);
            console.log('📄 HTML内容已保存，长度:', htmlData?.content?.length);
          } else {
            throw new Error('PNG图片生成失败：未返回图片URL');
          }
        } else {
          throw new Error(responseData.error || '图片生成失败');
        }
      } else {
        throw new Error('云函数调用失败');
      }

    } catch (error) {
      console.error('❌ PNG图片生成失败:', error);

      this.setData({
        isGeneratingImage: false,
        imageError: true
      });

      wx.showToast({
        title: error.message || '图片生成失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 开始倒计时
  startCountdown() {
    let timeLeft = 25;
    const timer = setInterval(() => {
      timeLeft--;
      this.setData({ estimatedTime: timeLeft });

      if (timeLeft <= 0 || !this.data.isGeneratingImage) {
        clearInterval(timer);
      }
    }, 1000);
  },

  // 获取选择的模板类型
  getSelectedTemplate() {
    const templates = ['professional', 'modern', 'minimal'];
    return templates[this.data.selectedTemplate] || 'professional';
  },

  // 图片加载完成
  onImageLoad(e) {
    console.log('图片加载完成:', e.detail);
  },

  // 图片加载错误
  onImageError(e) {
    console.error('图片加载错误:', e.detail);
    this.setData({ imageError: true });

    wx.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 2000
    });
  },

  // 预览图片
  previewImage() {
    if (!this.data.resumeImageUrl) {
      wx.showToast({
        title: '没有可预览的图片',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.previewImage({
      urls: [this.data.resumeImageUrl],
      current: this.data.resumeImageUrl
    });
  },

  // 保存图片到相册
  async saveImageToAlbum() {
    if (!this.data.resumeImageUrl) {
      wx.showToast({
        title: '没有可保存的图片',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    try {
      // 请求保存到相册权限
      const authResult = await wx.getSetting();

      if (!authResult.authSetting['scope.writePhotosAlbum']) {
        const authorizeResult = await wx.authorize({
          scope: 'scope.writePhotosAlbum'
        });
      }

      // 下载图片
      wx.showLoading({ title: '正在保存...' });

      const downloadResult = await wx.downloadFile({
        url: this.data.resumeImageUrl
      });

      // 保存到相册
      await wx.saveImageToPhotosAlbum({
        filePath: downloadResult.tempFilePath
      });

      wx.hideLoading();
      wx.showToast({
        title: '保存成功',
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      wx.hideLoading();
      console.error('保存图片失败:', error);

      if (error.errMsg && error.errMsg.includes('auth deny')) {
        wx.showModal({
          title: '需要授权',
          content: '需要您授权保存图片到相册',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      } else {
        wx.showToast({
          title: '保存失败',
          icon: 'none',
          duration: 2000
        });
      }
    }
  }
})