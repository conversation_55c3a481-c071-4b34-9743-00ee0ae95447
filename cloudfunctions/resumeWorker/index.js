/**
 * Resume Worker - 腾讯云混元版本
 * 使用腾讯云混元大模型进行简历解析
 */

'use strict';

const cloud = require('wx-server-sdk');
const cloudbase = require("@cloudbase/js-sdk");
const adapter = require("@cloudbase/adapter-node");
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 初始化云开发环境 - 这是使用AI功能的前提
cloud.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

// 初始化CloudBase AI SDK
const { sessionStorage } = adapter.genAdapter();
cloudbase.useAdapters(adapter);

const cbApp = cloudbase.init({
  env: "zemuresume-4gjvx1wea78e3d1e",
  // 使用API key进行认证
  apiKey: "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
});

// 初始化腾讯云OCR客户端
const OcrClient = tencentcloud.ocr.v20181119.Client;

// 从环境变量获取腾讯云密钥
const clientConfig = {
  credential: {
    secretId: process.env.TENCENT_SECRET_ID || 'YOUR_SECRET_ID',
    secretKey: process.env.TENCENT_SECRET_KEY || 'YOUR_SECRET_KEY',
  },
  region: 'ap-guangzhou',
  profile: {
    httpProfile: {
      endpoint: 'ocr.tencentcloudapi.com',
    },
  },
};

const ocrClient = new OcrClient(clientConfig);

/**
 * 主处理函数 - 异步处理版本
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('🚀 ResumeWorker 开始异步处理请求', { event, context });

  const db = cloud.database();
  let taskId = null;

  try {
    // 解析输入数据
    let requestData = {};
    if (typeof event === 'string') {
      requestData = JSON.parse(event);
    } else if (event.body) {
      requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } else {
      requestData = event;
    }

    const { taskId: inputTaskId, resumeContent, fileName, fileType, fileId, cloudPath, aiResult } = requestData;
    taskId = inputTaskId;

    // 如果有taskId，更新任务状态为processing
    if (taskId) {
      console.log('📝 更新任务状态为processing:', taskId);
      await db.collection('resumeTasks').where({
        taskId
      }).update({
        data: {
          status: 'processing',
          startedAt: new Date(),
          updatedAt: new Date(),
          progress: '开始处理简历解析...'
        }
      });
    }

    // 如果提供了AI解析结果，直接使用
    if (aiResult) {
      console.log('📄 使用提供的AI解析结果');

      if (taskId) {
        await updateTaskProgress(db, taskId, '正在生成积木...');
      }

      const parseResult = parseAIResponse(aiResult);

      // 生成积木
      const bricks = generateBricks(
        parseResult.personalInfo,
        parseResult.workExperience || [],
        parseResult.education || [],
        parseResult.skills || [],
        parseResult.projects || []
      );

      // 添加积木到结果中
      parseResult.bricks = bricks;
      parseResult.certifications = parseResult.certifications || [];
      parseResult.languages = parseResult.personalInfo?.languages || [];

      const processingTime = Date.now() - startTime;
      console.log('✅ AI简历解析完成', { processingTime });

      const result = {
        success: true,
        data: parseResult,
        model: 'deepseek-v3',
        processingTime
      };

      // 如果有taskId，更新任务状态为completed
      if (taskId) {
        await updateTaskCompleted(db, taskId, result, processingTime);
        console.log('✅ 任务状态已更新为completed');
        return; // 异步处理，不需要返回响应
      }

      return createSuccessResponse(result);
    }

    // 检查是否有简历内容或文件ID
    if (!resumeContent && !fileId && !cloudPath) {
      const errorMsg = '缺少简历内容参数 (resumeContent 或 fileId/cloudPath)';
      if (taskId) {
        await updateTaskFailed(db, taskId, errorMsg);
        return;
      }
      return createErrorResponse(errorMsg);
    }

    console.log('📄 开始简历解析', { fileName, fileType });

    // 如果没有resumeContent但有fileId，先通过OCR获取内容
    let actualResumeContent = resumeContent;
    if (!actualResumeContent && (fileId || cloudPath)) {
      console.log('📄 检测到文件ID，调用OCR获取内容');

      if (taskId) {
        await updateTaskProgress(db, taskId, '正在提取文件内容...');
      }

      actualResumeContent = await getContentFromFile(fileId, cloudPath, fileName);
    }

    if (taskId) {
      await updateTaskProgress(db, taskId, '正在调用AI解析简历...');
    }

    // 使用云开发AI能力调用混元模型
    const parseResult = await parseResumeWithHunyuan(actualResumeContent, fileName, fileType);

    const processingTime = Date.now() - startTime;
    console.log('✅ 简历解析完成', { processingTime });

    const result = {
      success: true,
      data: parseResult,
      model: 'deepseek-v3',
      processingTime
    };

    // 如果有taskId，更新任务状态为completed
    if (taskId) {
      await updateTaskCompleted(db, taskId, result, processingTime);
      console.log('✅ 任务状态已更新为completed');
      return; // 异步处理，不需要返回响应
    }

    return createSuccessResponse(result);

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ 简历解析失败:', error);

    // 如果有taskId，更新任务状态为failed
    if (taskId) {
      await updateTaskFailed(db, taskId, error.message, processingTime);
      console.log('❌ 任务状态已更新为failed');
      return; // 异步处理，不需要返回响应
    }

    return createErrorResponse(error.message, {
      processingTime,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 从文件ID获取内容（直接进行OCR处理，避免函数间调用）
 */
async function getContentFromFile(fileId, cloudPath, fileName) {
  try {
    console.log('📄 开始从文件获取内容', { fileId, cloudPath, fileName });

    // 直接从云存储下载文件
    let downloadResult;
    if (fileId) {
      console.log('📥 通过fileId下载文件...');
      downloadResult = await cloud.downloadFile({
        fileID: fileId
      });
    } else if (cloudPath) {
      console.log('📥 通过cloudPath下载文件...');
      // 如果是cloudPath，需要构造完整的fileID
      const fullFileId = `cloud://zemuresume-4gjvx1wea78e3d1e.7a65-zemuresume-4gjvx1wea78e3d1e-1341667342/${cloudPath}`;
      downloadResult = await cloud.downloadFile({
        fileID: fullFileId
      });
    } else {
      throw new Error('缺少文件ID或云路径');
    }

    console.log('📥 文件下载完成，开始本地OCR识别');

    // 直接使用腾讯云OCR进行文本识别
    const parsedContent = await performOCRRecognition(downloadResult.fileContent, fileName);

    console.log('✅ OCR内容获取成功', { contentLength: parsedContent.length });
    return parsedContent;

  } catch (error) {
    console.error('❌ 从文件获取内容失败:', error);

    // 如果是OCR相关错误，提供更详细的错误信息
    if (error.message.includes('OCR') || error.message.includes('识别')) {
      console.error('⚠️ OCR识别失败，可能是文件格式或内容问题');
      return `OCR识别失败: ${error.message}。请检查文件是否为有效的PDF格式，或尝试重新上传。`;
    }

    // 其他错误，返回默认内容，避免完全失败
    return `文件内容获取失败: ${error.message}。请检查文件格式或重新上传。`;
  }
}

/**
 * 使用腾讯云开发原生AI功能解析简历 - DeepSeek模型
 * 严格禁止使用降级方案和模拟数据，必须使用真实AI功能
 */
async function parseResumeWithHunyuan(resumeContent, fileName, fileType) {
  console.log('🤖 开始简历解析（使用腾讯云开发原生AI - DeepSeek模型）');
  console.log('📄 解析简历内容长度:', resumeContent.length);

  // 构建AI prompt，明确指导分类和项目合并
  const prompt = `请分析以下简历内容，并按照指定的四个分类提取信息，返回JSON格式：

简历内容：
${resumeContent}

请严格按照以下分类提取信息：

1. 个人信息 (personal) - 姓名、联系方式、地址等基本信息
2. 教育背景 (education) - 学校、学历、专业、毕业时间等
3. 工作经历 (experience) - 公司、职位、工作时间、工作内容等
4. 能力技能 (skills) - 专业技能、技术栈、语言能力、证书等

⚠️ 重要要求 - 项目识别精准度：
- 对于工作经历中的responsibilities和achievements，请智能合并相关内容
- 同一个项目的不同方面（如动作、过程、结果）应该合并为一个完整的项目描述
- 避免将项目的动作和结果分别识别为两个不同的项目
- 每个responsibility应该是一个完整的项目或任务，包含背景、行动和结果
- 每个achievement应该是一个具体的成果，与对应的responsibility相关联

要求：
- 工作经历要按职位分组，每个职位下的项目要合理合并
- 技能要详细分类，包括技术技能、软技能、语言能力等
- 如果信息不明确，请合理推断但标注置信度
- 返回标准JSON格式，不要包含其他文字

JSON格式示例：
{
  "personalInfo": {
    "name": "姓名",
    "email": "邮箱",
    "phone": "电话",
    "location": "地址",
    "title": "职位"
  },
  "education": [
    {
      "school": "学校名称",
      "degree": "学历",
      "major": "专业",
      "year": "时间",
      "honors": []
    }
  ],
  "workExperience": [
    {
      "company": "公司名称",
      "position": "职位",
      "duration": "工作时间",
      "responsibilities": ["职责1", "职责2"],
      "achievements": ["成就1", "成就2"]
    }
  ],
  "skills": [
    {
      "name": "技能名称",
      "category": "技能分类",
      "proficiency": "熟练程度"
    }
  ],
  "projects": [
    {
      "name": "项目名称",
      "description": "项目描述",
      "technologies": ["技术1", "技术2"]
    }
  ],
  "summary": "个人简介"
}`;

  // 使用腾讯云开发原生AI功能调用DeepSeek模型
  console.log('🤖 调用腾讯云开发原生AI - DeepSeek模型...');

  // 使用腾讯云开发原生AI功能调用DeepSeek模型
  console.log('✅ 开始调用腾讯云开发原生AI功能...');
  console.log('环境ID:', 'zemuresume-4gjvx1wea78e3d1e');

  try {
    // 初始化auth并进行匿名登录
    console.log('🔐 初始化认证...');
    const auth = cbApp.auth({
      storage: sessionStorage,
      captchaOptions: {
        openURIWithCallback: (...props) =>
          console.log("open uri with callback", ...props),
      },
    });

    // 匿名登录
    await auth.signInAnonymously();
    console.log('✅ 匿名登录成功');

    // 获取AI模块
    const ai = await cbApp.ai();
    console.log('✅ AI模块初始化成功');

    // 创建DeepSeek模型
    const aiModel = ai.createModel("deepseek");
    console.log('✅ DeepSeek模型创建成功');

    // 调用AI模型进行文本生成 - 增加超时和重试机制
    console.log('🚀 开始调用AI模型生成文本...');

    let aiResult;
    let retryCount = 0;
    const maxRetries = 3;
    const timeoutMs = 60000; // 60秒超时

    while (retryCount < maxRetries) {
      try {
        console.log(`🔄 AI调用尝试 ${retryCount + 1}/${maxRetries}`);

        // 使用Promise.race实现超时控制
        aiResult = await Promise.race([
          aiModel.generateText({
            model: "deepseek-v3",
            messages: [
              {
                role: "user",
                content: prompt
              }
            ]
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('AI调用超时')), timeoutMs)
          )
        ]);

        console.log('✅ AI调用成功');
        break;

      } catch (error) {
        retryCount++;
        console.error(`❌ AI调用失败 (尝试 ${retryCount}/${maxRetries}):`, error.message);

        if (retryCount >= maxRetries) {
          throw new Error(`AI服务调用失败，已重试${maxRetries}次: ${error.message}`);
        }

        // 指数退避重试
        const delay = Math.min(1000 * Math.pow(2, retryCount - 1), 10000);
        console.log(`⏳ ${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    console.log('🤖 DeepSeek AI调用成功');
    console.log('🤖 AI响应结构:', JSON.stringify(aiResult, null, 2));

    // 提取AI响应内容 - 根据CloudBase AI SDK的响应格式
    let aiResponse = '';
    if (aiResult && aiResult.text) {
      // CloudBase AI SDK的标准响应格式
      aiResponse = aiResult.text;
    } else if (aiResult && aiResult.data && aiResult.data.choices && aiResult.data.choices[0]) {
      // 备用响应格式
      aiResponse = aiResult.data.choices[0].message.content;
    } else if (aiResult && aiResult.choices && aiResult.choices[0]) {
      // 另一种备用响应格式
      aiResponse = aiResult.choices[0].message.content;
    } else {
      console.error('❌ AI响应格式错误:', JSON.stringify(aiResult));
      throw new Error(`AI响应格式错误，无法提取内容: ${JSON.stringify(aiResult)}`);
    }

    console.log('🤖 AI响应长度:', aiResponse.length);
    console.log('🤖 AI响应前200字符:', aiResponse.substring(0, 200));

    // 解析AI响应
    const parseResult = parseAIResponse(aiResponse);

    // 生成积木
    const bricks = generateBricks(
      parseResult.personalInfo,
      parseResult.workExperience || [],
      parseResult.education || [],
      parseResult.skills || [],
      parseResult.projects || []
    );

    // 🔧 新增：AI积木概括功能
    console.log('🤖 开始AI积木概括...');
    const enhancedBricks = await enhanceBricksWithAI(bricks, aiModel);
    console.log('✅ AI积木概括完成');

    // 添加积木到结果中
    parseResult.bricks = enhancedBricks;
    parseResult.certifications = parseResult.certifications || [];
    parseResult.languages = parseResult.personalInfo?.languages || [];

    console.log('✅ AI简历解析完成');
    console.log(`📊 AI解析结果统计: 工作经历${parseResult.workExperience?.length || 0}个, 教育背景${parseResult.education?.length || 0}个, 技能${parseResult.skills?.length || 0}个, 项目${parseResult.projects?.length || 0}个, 积木${enhancedBricks.length}个`);

    return parseResult;

  } catch (error) {
    console.error('❌ CloudBase AI调用失败:', error);
    throw new Error(`AI解析失败: ${error.message}`);
  }
}

// 所有规则解析函数已移除 - 严格禁止使用降级方案和模拟数据
// 只使用腾讯云开发原生AI功能进行简历解析

// 所有规则解析辅助函数已移除 - 严格禁止使用降级方案

/**
 * 生成能力积木
 */
function generateBricks(personalInfo, workExperience, education, skills, projects) {
  const bricks = [];
  const timestamp = new Date().toISOString();

  // 1. 生成个人信息积木
  if (personalInfo && personalInfo.name) {
    bricks.push({
      id: `personal_${Date.now()}`,
      title: '个人信息',
      description: `姓名：${personalInfo.name || '未知'}\n联系方式：${personalInfo.phone || ''} ${personalInfo.email || ''}\n地址：${personalInfo.location || ''}\n职位：${personalInfo.title || ''}`,
      category: 'personal',
      keywords: ['个人信息', '联系方式', personalInfo.name].filter(Boolean),
      confidence: 1.0,
      usageCount: 0,
      level: '基础',
      createTime: timestamp,
      updateTime: timestamp,
      source: 'resume_upload',
      data: personalInfo
    });
  }

  // 2. 生成教育背景积木
  if (education && Array.isArray(education) && education.length > 0) {
    education.forEach((edu, index) => {
      bricks.push({
        id: `education_${Date.now()}_${index}`,
        title: `${edu.school || '学校'} - ${edu.degree || '学历'}`,
        description: `学历：${edu.degree || ''}\n时间：${edu.year || ''}\n学校：${edu.school || ''}`,
        category: 'education',
        keywords: ['教育背景', edu.degree, edu.school].filter(Boolean),
        confidence: 0.9,
        usageCount: 0,
        level: '基础',
        createTime: timestamp,
        updateTime: timestamp,
        source: 'resume_upload',
        data: edu
      });
    });
  }

  // 3. 生成工作经历基础积木（仅包含基本信息）
  workExperience.forEach((exp, index) => {
    // 工作经历积木只保留基本信息：公司名称、职位、时长
    bricks.push({
      id: `work_basic_${Date.now()}_${index}`,
      title: `${exp.position} - ${exp.company}`,
      description: `职位：${exp.position}\n公司：${exp.company}\n时间：${exp.duration}`,
      category: 'experience',
      confidence: 0.9,
      keywords: [exp.company, exp.position, '工作经历'],
      usageCount: 0,
      level: '基础',
      createTime: timestamp,
      updateTime: timestamp,
      source: 'resume_upload',
      data: {
        company: exp.company,
        position: exp.position,
        duration: exp.duration,
        type: 'basic_info'
      }
    });

    // 🔧 优化：智能合并相关项目，避免动作和结果分离
    if (exp.responsibilities && exp.responsibilities.length > 0) {
      exp.responsibilities.forEach((responsibility, respIndex) => {
        // 查找相关的achievements
        const relatedAchievements = exp.achievements ?
          exp.achievements.filter(achievement =>
            // 简单的关键词匹配来找到相关成就
            hasCommonKeywords(responsibility, achievement)
          ) : [];

        // 构建完整的项目描述
        let fullDescription = responsibility;
        if (relatedAchievements.length > 0) {
          fullDescription += '。成果：' + relatedAchievements.join('；');
        }

        const projectKeywords = extractProjectKeywords(fullDescription, exp.company, exp.position);

        bricks.push({
          id: `project_${Date.now()}_${index}_${respIndex}`,
          title: generateProjectTitleFallback(fullDescription, exp.position),
          description: fullDescription,
          category: 'project',
          confidence: 0.85,
          keywords: projectKeywords.abilities, // 4个关键能力标签
          usageCount: 0,
          level: determineProjectLevel(fullDescription),
          createTime: timestamp,
          updateTime: timestamp,
          source: 'resume_upload',
          data: {
            project: responsibility,
            achievements: relatedAchievements,
            company: exp.company, // 公司标签
            position: exp.position,
            duration: exp.duration,
            abilities: projectKeywords.abilities,
            companyTag: projectKeywords.companyTag,
            isEnhanced: true // 标记为增强版积木
          }
        });
      });
    }

    // 处理未匹配的成就作为独立积木
    if (exp.achievements && exp.achievements.length > 0) {
      const unmatchedAchievements = exp.achievements.filter(achievement => {
        // 检查是否已经被某个responsibility匹配
        return !exp.responsibilities?.some(responsibility =>
          hasCommonKeywords(responsibility, achievement)
        );
      });

      unmatchedAchievements.forEach((achievement, achIndex) => {
        const achievementKeywords = extractProjectKeywords(achievement, exp.company, exp.position);

        bricks.push({
          id: `achievement_${Date.now()}_${index}_${achIndex}`,
          title: generateProjectTitleFallback(achievement, exp.position),
          description: achievement,
          category: 'project',
          confidence: 0.9,
          keywords: achievementKeywords.abilities,
          usageCount: 0,
          level: '高级',
          createTime: timestamp,
          updateTime: timestamp,
          source: 'resume_upload',
          data: {
            project: achievement,
            company: exp.company,
            position: exp.position,
            duration: exp.duration,
            abilities: achievementKeywords.abilities,
            companyTag: achievementKeywords.companyTag,
            type: 'achievement'
          }
        });
      });
    }
  });

  // 4. 基于技能生成积木 - 已移除（用户要求去掉技能特长积木）
  // 技能信息已经通过项目积木的能力标签体现，不再单独生成技能积木
  console.log('⚠️ 已跳过技能积木生成（改为通过项目积木的能力标签体现）');

  // 5. 基于项目生成积木
  projects.forEach((project, index) => {
    bricks.push({
      id: `project_${Date.now()}_${index}`,
      title: project.name,
      description: project.description,
      category: 'project', // 修复：使用前端期望的分类
      confidence: 0.85,
      keywords: [project.name, '项目', '管理'],
      usageCount: 0,
      level: '高级',
      createTime: timestamp,
      updateTime: timestamp,
      source: 'resume_upload',
      data: project
    });
  });

  // 按时间和公司标签排序：项目积木按公司时间降序，其他积木保持原顺序
  const sortedBricks = bricks.sort((a, b) => {
    // 项目类积木按公司时间排序
    if (a.category === 'project' && b.category === 'project') {
      // 提取时间信息进行排序（最新的在前）
      const aTime = a.data?.duration || '';
      const bTime = b.data?.duration || '';

      // 简单的时间比较（假设格式为 YYYY.MM-YYYY.MM）
      const aYear = parseInt(aTime.split('-')[1]?.split('.')[0] || '0');
      const bYear = parseInt(bTime.split('-')[1]?.split('.')[0] || '0');

      return bYear - aYear; // 降序排列
    }

    // 其他积木保持原有顺序
    return 0;
  });

  console.log(`📊 积木生成完成: 总计${sortedBricks.length}个积木`);
  console.log(`   - 个人信息: ${sortedBricks.filter(b => b.category === 'personal').length}个`);
  console.log(`   - 教育背景: ${sortedBricks.filter(b => b.category === 'education').length}个`);
  console.log(`   - 工作经历: ${sortedBricks.filter(b => b.category === 'experience').length}个`);
  console.log(`   - 项目能力: ${sortedBricks.filter(b => b.category === 'project').length}个`);
  console.log(`   - 技能专长: ${sortedBricks.filter(b => b.category === 'skills').length}个`);

  return sortedBricks;
}

/**
 * 从项目描述中提取关键词
 */
function extractProjectKeywords(description, company, position) {
  // 能力关键词库
  const abilityKeywords = {
    '管理': ['管理', '运营', '团队', '领导', '协调', '规划'],
    '数据分析': ['数据', '分析', '统计', '指标', '报告', '监控'],
    '产品': ['产品', '功能', '需求', '设计', '优化', '迭代'],
    '营销': ['营销', '推广', '获客', '转化', '增长', '渠道'],
    '技术': ['开发', '技术', '系统', '平台', '工具', '架构'],
    '沟通': ['沟通', '协作', '合作', '对接', '联系', '交流'],
    '创新': ['创新', '改进', '优化', '提升', '突破', '创造'],
    '执行': ['执行', '落地', '实施', '推进', '完成', '达成']
  };

  const foundAbilities = [];
  const descLower = description.toLowerCase();

  // 提取能力标签
  Object.entries(abilityKeywords).forEach(([ability, keywords]) => {
    if (keywords.some(keyword => descLower.includes(keyword))) {
      foundAbilities.push(ability);
    }
  });

  // 确保至少有4个能力标签，不足的用通用标签补充
  const defaultAbilities = ['执行力', '专业能力', '团队协作', '问题解决'];
  while (foundAbilities.length < 4) {
    const defaultAbility = defaultAbilities[foundAbilities.length % defaultAbilities.length];
    if (!foundAbilities.includes(defaultAbility)) {
      foundAbilities.push(defaultAbility);
    }
  }

  // 只取前4个
  const abilities = foundAbilities.slice(0, 4);

  // 生成公司标签（用于排序和识别）
  const companyTag = `${company}_${position}`;

  return {
    abilities,
    companyTag
  };
}

/**
 * 生成项目标题 - AI分析版
 */
async function generateProjectTitle(description, position) {
  try {
    // 使用AI分析项目描述，生成准确的标题
    const aiTitle = await analyzeProjectWithAI(description, position);
    if (aiTitle && aiTitle.trim()) {
      return aiTitle.trim();
    }
  } catch (error) {
    console.warn('⚠️ AI标题生成失败，使用备用方案:', error);
  }

  // 备用方案：基于规则的标题生成
  return generateProjectTitleFallback(description, position);
}

/**
 * AI分析项目描述
 */
async function analyzeProjectWithAI(description, position) {
  try {
    const cloudbase = require('@cloudbase/node-sdk');
    const app = cloudbase.init({
      env: process.env.TCB_ENV || 'zemuresume-4gjvx1wea78e3d1e'
    });

    // 使用云开发AI能力
    const systemPrompt = `你是一个专业的简历分析师。请根据项目描述，生成一个简洁准确的项目标题。

要求：
1. 标题要体现项目的核心能力和业务价值
2. 长度控制在8个字以内
3. 格式：[核心能力][业务领域][经验/能力]
4. 例如：跨境商家管理经验、商家赋能体系搭建、广告SOP搭建经验

参考示例：
- "管理全年8w+的跨境卖家全生命周期" → "跨境商家管理经验"
- "搭建多层次的商家赋能体系" → "商家赋能体系搭建"
- "助商家ACOS降低35%" → "广告SOP搭建经验"

请只返回标题，不要其他内容。`;

    const userInput = `职位：${position}\n项目描述：${description}`;

    // 调用AI服务
    const response = await app.callFunction({
      name: 'ai-analysis',
      data: {
        systemPrompt,
        userInput,
        model: 'deepseek-v3'
      }
    });

    if (response.result && response.result.success) {
      const cleanTitle = response.result.data
        .replace(/["""'']/g, '')
        .replace(/^[：:：\s]+/, '')
        .replace(/[：:：\s]+$/, '')
        .trim();

      console.log(`🤖 AI生成标题: "${cleanTitle}" (原描述: ${description.substring(0, 30)}...)`);
      return cleanTitle;
    }
  } catch (error) {
    console.error('❌ AI标题生成失败:', error);
    throw error;
  }
}

/**
 * 备用标题生成方案
 */
function generateProjectTitleFallback(description, position) {
  // 清理描述文本
  const cleanDesc = description.replace(/[，。！？；：""''（）【】]/g, ' ').trim();

  // 提取关键业务动词
  const actionKeywords = cleanDesc.match(/(负责|管理|运营|开发|设计|优化|提升|增长|创建|搭建|实现|建立|推进|执行|完成|达成|助力|帮助|支持|协调|策划|制定|落地|推出|上线)/g);

  // 提取业务对象/领域
  const businessObjects = cleanDesc.match(/(客户|用户|商家|卖家|产品|系统|平台|项目|团队|业务|市场|销售|营销|推广|运营|数据|分析|策略|方案|流程|体系|工具|功能|服务|渠道|社群)/g);

  // 智能生成标题
  let title = '';

  // 优先级1：动作 + 业务对象
  if (actionKeywords && businessObjects) {
    const action = actionKeywords[0];
    const object = businessObjects[0];
    title = `${action}${object}经验`;
  }
  // 优先级2：仅动作
  else if (actionKeywords) {
    const action = actionKeywords[0];
    title = `${action}能力经验`;
  }
  // 优先级3：仅业务对象
  else if (businessObjects) {
    const object = businessObjects[0];
    title = `${object}相关经验`;
  }
  // 兜底：使用职位
  else {
    title = `${position}核心经验`;
  }

  // 确保标题长度合理
  if (title.length > 8) {
    title = title.substring(0, 8);
  }

  return title;
}

/**
 * 判断项目等级
 */
function determineProjectLevel(description) {
  const desc = description.toLowerCase();

  // 高级项目指标
  const advancedIndicators = ['亿', '千万', '百万', '年销', '总gms', '卓越', '突破'];
  // 中级项目指标
  const intermediateIndicators = ['万', '千', '提升', '优化', '改进', '增长'];

  if (advancedIndicators.some(indicator => desc.includes(indicator))) {
    return '高级';
  } else if (intermediateIndicators.some(indicator => desc.includes(indicator))) {
    return '中级';
  } else {
    return '基础';
  }
}

/**
 * 检查两个文本是否有共同关键词 - 用于匹配相关的责任和成就
 */
function hasCommonKeywords(text1, text2) {
  // 提取关键词的正则表达式
  const keywordRegex = /(负责|管理|运营|开发|设计|优化|提升|增长|创建|搭建|实现|建立|推进|执行|完成|达成|助力|帮助|支持|协调|策划|制定|落地|推出|上线|客户|用户|商家|卖家|产品|系统|平台|项目|团队|业务|市场|销售|营销|推广|运营|数据|分析|策略|方案|流程|体系|工具|功能|服务|渠道|社群)/g;

  const keywords1 = (text1.match(keywordRegex) || []).map(k => k.toLowerCase());
  const keywords2 = (text2.match(keywordRegex) || []).map(k => k.toLowerCase());

  // 如果有2个或以上共同关键词，认为是相关的
  const commonKeywords = keywords1.filter(k => keywords2.includes(k));
  return commonKeywords.length >= 2;
}

/**
 * 解析AI返回的JSON响应
 */
function parseAIResponse(response) {
  try {
    // 尝试直接解析JSON
    if (response.startsWith('{') || response.startsWith('[')) {
      return JSON.parse(response);
    }

    // 如果响应包含markdown代码块，提取JSON部分
    const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[1]);
    }

    // 查找第一个完整的JSON对象
    const startIndex = response.indexOf('{');
    const endIndex = response.lastIndexOf('}');
    if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
      const jsonStr = response.substring(startIndex, endIndex + 1);
      return JSON.parse(jsonStr);
    }

    // 如果无法解析，返回基本结构
    throw new Error('无法解析AI响应为JSON格式');

  } catch (error) {
    console.error('❌ 解析AI响应失败:', error);
    console.log('原始响应:', response);

    // 返回基本的解析结果
    return {
      personalInfo: { name: "解析失败", email: "", phone: "", location: "" },
      workExperience: [],
      education: [],
      skills: [],
      projects: [],
      summary: "AI响应解析失败，请检查输入格式",
      bricks: []
    };
  }
}

/**
 * 创建成功响应
 */
function createSuccessResponse(data, metadata = {}) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: data,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e',
        ...metadata
      }
    })
  };
}

/**
 * 创建错误响应
 */
function createErrorResponse(message, metadata = {}) {
  return {
    statusCode: 500,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: false,
      error: message,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e',
        ...metadata
      }
    })
  };
}

/**
 * 使用腾讯云OCR进行PDF文本识别 - 优化版本，支持并行处理
 */
async function performOCRRecognition(fileContent, fileName) {
  try {
    console.log('🔍 开始腾讯云OCR识别', { fileName, fileSize: fileContent.length });

    // 将文件内容转换为Base64
    const base64Content = fileContent.toString('base64');

    // 直接处理常见的PDF页数（1-5页），避免耗时的页数检测
    const maxPages = 5;
    console.log(`📄 开始并行处理PDF页面，最多处理${maxPages}页`);

    // 并行处理所有可能的页面
    const pagePromises = [];
    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      pagePromises.push(processPage(base64Content, pageNum));
    }

    // 等待所有页面处理完成
    const pageResults = await Promise.all(pagePromises);

    // 过滤出成功的页面结果
    const validPages = pageResults.filter(result => result.success);
    const allPageTexts = validPages.map(result => result.text);
    const totalDetections = validPages.reduce((sum, result) => sum + result.detections, 0);

    console.log(`✅ 成功处理${validPages.length}页PDF，总检测数: ${totalDetections}`);

    // 合并所有页面的文本
    const finalText = allPageTexts.join('\n\n');

    console.log('✅ 并行PDF OCR识别完成', {
      totalPages: validPages.length,
      totalTextLength: finalText.length,
      totalDetections: totalDetections
    });

    return finalText;

  } catch (error) {
    console.error('❌ OCR识别失败:', error);

    // OCR失败时返回错误信息，但不中断流程
    return `OCR识别失败: ${error.message}。请检查文件格式或网络连接。`;
  }
}

/**
 * 处理单个PDF页面的OCR识别
 */
async function processPage(base64Content, pageNum) {
  try {
    console.log(`🔍 正在处理第${pageNum}页...`);

    // 调用腾讯云通用印刷体识别API
    const params = {
      ImageBase64: base64Content,
      LanguageType: 'zh',  // 中英文混合
      IsPdf: true,         // 指定为PDF文件
      PdfPageNumber: pageNum
    };

    const response = await ocrClient.GeneralBasicOCR(params);

    if (response.TextDetections && response.TextDetections.length > 0) {
      // 提取识别到的文本
      const pageText = response.TextDetections
        .map(detection => detection.DetectedText)
        .join('\n');

      console.log(`✅ 第${pageNum}页OCR识别成功`, {
        detectionsCount: response.TextDetections.length,
        textLength: pageText.length
      });

      return {
        success: true,
        pageNum: pageNum,
        text: `--- 第${pageNum}页 ---\n${pageText}`,
        detections: response.TextDetections.length
      };
    } else {
      console.warn(`⚠️ 第${pageNum}页未识别到文本内容`);
      return {
        success: false,
        pageNum: pageNum,
        text: '',
        detections: 0
      };
    }

  } catch (pageError) {
    // 如果是页面不存在的错误，静默处理
    if (pageError.message && (
      pageError.message.includes('page') ||
      pageError.message.includes('页') ||
      pageError.message.includes('InvalidParameter')
    )) {
      console.log(`📄 第${pageNum}页不存在，跳过处理`);
      return {
        success: false,
        pageNum: pageNum,
        text: '',
        detections: 0
      };
    }

    console.error(`❌ 第${pageNum}页OCR识别失败:`, pageError.message);
    return {
      success: false,
      pageNum: pageNum,
      text: '',
      detections: 0
    };
  }
}



/**
 * AI积木概括功能 - 为每个积木生成精准的标题和描述
 */
async function enhanceBricksWithAI(bricks, aiModel) {
  console.log('🤖 开始AI积木概括，积木数量:', bricks.length);

  const enhancedBricks = [];

  // 分批处理积木，避免AI调用过于频繁
  const batchSize = 5;
  for (let i = 0; i < bricks.length; i += batchSize) {
    const batch = bricks.slice(i, i + batchSize);

    try {
      // 构建批量概括prompt
      const batchPrompt = `请为以下积木生成精准的标题和描述概括。每个积木都需要一个简洁有力的标题（6-8个字）和一个精炼的描述（20-30个字）。

积木列表：
${batch.map((brick, index) => `
${index + 1}. 原标题: ${brick.title}
   原描述: ${brick.description}
   类别: ${brick.category}
   公司: ${brick.data?.company || ''}
   职位: ${brick.data?.position || ''}
`).join('')}

要求：
1. 标题要体现核心能力或成果，避免泛泛而谈
2. 描述要突出具体的行动、方法和结果
3. 保持专业性和准确性
4. 返回JSON格式，包含title和description字段

返回格式：
[
  {
    "title": "精准标题",
    "description": "精炼描述"
  }
]`;

      console.log(`🤖 处理第${Math.floor(i / batchSize) + 1}批积木...`);

      // 使用超时控制的AI调用
      const aiResult = await Promise.race([
        aiModel.generateText({
          model: "deepseek-v3",
          messages: [
            {
              role: "user",
              content: batchPrompt
            }
          ]
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI积木概括超时')), 30000)
        )
      ]);

      // 提取AI响应
      let aiResponse = '';
      if (aiResult && aiResult.text) {
        aiResponse = aiResult.text;
      } else {
        console.warn('⚠️ AI积木概括响应格式异常，使用原始积木');
        enhancedBricks.push(...batch);
        continue;
      }

      // 解析AI响应
      try {
        const enhancements = JSON.parse(aiResponse.replace(/```json|```/g, '').trim());

        if (Array.isArray(enhancements) && enhancements.length === batch.length) {
          // 应用AI概括结果
          batch.forEach((brick, index) => {
            const enhancement = enhancements[index];
            if (enhancement && enhancement.title && enhancement.description) {
              enhancedBricks.push({
                ...brick,
                title: enhancement.title,
                description: enhancement.description,
                originalTitle: brick.title,
                originalDescription: brick.description,
                enhancedByAI: true
              });
            } else {
              enhancedBricks.push(brick);
            }
          });
        } else {
          console.warn('⚠️ AI概括结果数量不匹配，使用原始积木');
          enhancedBricks.push(...batch);
        }
      } catch (parseError) {
        console.warn('⚠️ AI概括结果解析失败，使用原始积木:', parseError);
        enhancedBricks.push(...batch);
      }

      // 添加延迟，避免AI调用过于频繁
      if (i + batchSize < bricks.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

    } catch (error) {
      console.warn('⚠️ AI积木概括失败，使用原始积木:', error);
      enhancedBricks.push(...batch);
    }
  }

  console.log('✅ AI积木概括完成，增强积木数量:', enhancedBricks.length);
  return enhancedBricks;
}

/**
 * 更新任务进度
 */
async function updateTaskProgress(db, taskId, progress) {
  try {
    await db.collection('resumeTasks').where({
      taskId
    }).update({
      data: {
        progress,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error('❌ 更新任务进度失败:', error);
  }
}

/**
 * 更新任务为完成状态
 */
async function updateTaskCompleted(db, taskId, result, processingTime) {
  try {
    // 使用set方法替代update，避免字段创建问题
    const updateData = {
      status: 'completed',
      result: result,
      completedAt: new Date(),
      updatedAt: new Date(),
      processingTime: processingTime,
      progress: '任务完成'
    };

    console.log('📝 更新任务完成状态:', taskId, '结果大小:', JSON.stringify(result).length);

    await db.collection('resumeTasks').where({
      taskId
    }).update({
      data: updateData
    });

    console.log('✅ 任务完成状态更新成功');
  } catch (error) {
    console.error('❌ 更新任务完成状态失败:', error);

    // 如果update失败，尝试使用doc().set()方法
    try {
      console.log('🔄 尝试使用set方法更新任务状态...');

      // 先查询现有记录
      const existingTask = await db.collection('resumeTasks').where({
        taskId
      }).get();

      if (existingTask.data && existingTask.data.length > 0) {
        const taskDoc = existingTask.data[0];
        const docId = taskDoc._id;

        // 合并现有数据和新数据
        const mergedData = {
          ...taskDoc,
          status: 'completed',
          result: result,
          completedAt: new Date(),
          updatedAt: new Date(),
          processingTime: processingTime,
          progress: '任务完成'
        };

        // 删除_id字段，避免冲突
        delete mergedData._id;

        await db.collection('resumeTasks').doc(docId).set({
          data: mergedData
        });

        console.log('✅ 使用set方法更新任务状态成功');
      }
    } catch (setError) {
      console.error('❌ 使用set方法更新任务状态也失败:', setError);
    }
  }
}

/**
 * 更新任务为失败状态
 */
async function updateTaskFailed(db, taskId, errorMessage, processingTime = null) {
  try {
    await db.collection('resumeTasks').where({
      taskId
    }).update({
      data: {
        status: 'failed',
        error: errorMessage,
        completedAt: new Date(),
        updatedAt: new Date(),
        processingTime
      }
    });
  } catch (error) {
    console.error('❌ 更新任务失败状态失败:', error);
  }
}

// 所有增强版解析函数已移除 - 严格禁止使用降级方案和模拟数据
