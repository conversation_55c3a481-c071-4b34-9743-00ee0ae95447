/**
 * 全局轮询管理器 - 解决resumeTaskQuery高频轮询问题
 * 确保同一任务只有一个轮询实例，避免重复调用
 */

class PollingManager {
  constructor() {
    this.activePolls = new Map(); // 存储活跃的轮询任务
    this.pollingInterval = 15000; // 统一15秒间隔
    this.maxPollingTime = 300000; // 最大轮询5分钟
  }

  /**
   * 开始轮询任务状态
   * @param {string} taskId - 任务ID
   * @param {Function} onProgress - 进度回调函数
   * @param {Function} onComplete - 完成回调函数
   * @param {Function} onError - 错误回调函数
   * @returns {boolean} 是否成功启动轮询
   */
  startPolling(taskId, onProgress, onComplete, onError) {
    // 检查是否已经在轮询这个任务
    if (this.activePolls.has(taskId)) {
      console.warn(`⚠️ 任务 ${taskId} 已在轮询中，跳过重复启动`);
      return false;
    }

    console.log(`🔄 启动任务轮询: ${taskId}`);

    const pollData = {
      taskId,
      onProgress,
      onComplete,
      onError,
      startTime: Date.now(),
      attemptCount: 0,
      timer: null
    };

    // 立即执行第一次查询
    this.executeQuery(pollData);

    // 设置定时轮询
    pollData.timer = setInterval(() => {
      this.executeQuery(pollData);
    }, this.pollingInterval);

    // 存储轮询数据
    this.activePolls.set(taskId, pollData);

    // 设置最大轮询时间限制
    setTimeout(() => {
      if (this.activePolls.has(taskId)) {
        console.warn(`⏰ 任务 ${taskId} 轮询超时，自动停止`);
        this.stopPolling(taskId);
        if (onError) {
          onError(new Error('轮询超时，请稍后手动查询任务状态'));
        }
      }
    }, this.maxPollingTime);

    return true;
  }

  /**
   * 停止轮询任务
   * @param {string} taskId - 任务ID
   */
  stopPolling(taskId) {
    const pollData = this.activePolls.get(taskId);
    if (pollData) {
      if (pollData.timer) {
        clearInterval(pollData.timer);
      }
      this.activePolls.delete(taskId);
      console.log(`🛑 停止任务轮询: ${taskId}`);
    }
  }

  /**
   * 执行查询
   * @param {Object} pollData - 轮询数据
   */
  async executeQuery(pollData) {
    const { taskId, onProgress, onComplete, onError } = pollData;
    pollData.attemptCount++;

    try {
      console.log(`🔍 第${pollData.attemptCount}次查询任务状态: ${taskId}`);

      const result = await wx.cloud.callFunction({
        name: 'resumeTaskQuery',
        data: {
          taskId: taskId,
          userId: wx.getStorageSync('userId') || 'anonymous'
        }
      });

      if (!result.result || !result.result.body) {
        throw new Error('查询任务状态失败');
      }

      const response = JSON.parse(result.result.body);
      if (!response.success) {
        throw new Error(response.error || '查询失败');
      }

      const task = response.data.data;

      // 调用进度回调
      if (onProgress) {
        onProgress(task);
      }

      // 检查任务状态
      if (task.status === 'completed') {
        console.log(`✅ 任务完成: ${taskId}`);
        this.stopPolling(taskId);
        if (onComplete) {
          onComplete(task);
        }
      } else if (task.status === 'failed') {
        console.log(`❌ 任务失败: ${taskId}`);
        this.stopPolling(taskId);
        if (onError) {
          onError(new Error(task.error || '任务处理失败'));
        }
      } else {
        console.log(`⏳ 任务进行中: ${taskId}, 状态: ${task.status}`);
        // 继续轮询
      }

    } catch (error) {
      console.error(`❌ 查询任务状态失败: ${taskId}`, error);

      // 检查是否是AI+服务超时错误
      if (error.message && error.message.includes('SERVICE_TIMEOUT')) {
        console.log(`⏰ AI+服务超时: ${taskId}`);
        this.stopPolling(taskId);
        if (onError) {
          const timeoutError = new Error('AI解析服务超时，请稍后重试或使用手动添加功能');
          timeoutError.code = 'AI_SERVICE_TIMEOUT';
          onError(timeoutError);
        }
        return;
      }

      // 网络错误继续重试，其他错误停止轮询
      if (error.message.includes('网络') || error.message.includes('timeout')) {
        console.log(`🔄 网络错误，继续重试: ${taskId}`);
      } else {
        this.stopPolling(taskId);
        if (onError) {
          onError(error);
        }
      }
    }
  }

  /**
   * 获取当前活跃轮询数量
   */
  getActivePollingCount() {
    return this.activePolls.size;
  }

  /**
   * 获取指定任务的轮询状态
   * @param {string} taskId - 任务ID
   */
  isPolling(taskId) {
    return this.activePolls.has(taskId);
  }

  /**
   * 停止所有轮询
   */
  stopAllPolling() {
    console.log(`🛑 停止所有轮询任务 (${this.activePolls.size}个)`);
    for (const taskId of this.activePolls.keys()) {
      this.stopPolling(taskId);
    }
  }

  /**
   * 设置轮询配置
   * @param {number} interval - 轮询间隔（毫秒）
   * @param {number} maxTime - 最大轮询时间（毫秒）
   */
  setConfig(interval, maxTime) {
    this.pollingInterval = interval;
    this.maxPollingTime = maxTime;
    console.log(`⚙️ 轮询配置已更新: 间隔${interval}ms, 最大时间${maxTime}ms`);
  }
}

// 创建全局实例
const pollingManager = new PollingManager();

// 页面卸载时清理所有轮询
if (typeof wx !== 'undefined') {
  const originalOnUnload = wx.onUnload || function () { };
  wx.onUnload = function () {
    pollingManager.stopAllPolling();
    originalOnUnload.apply(this, arguments);
  };
}

module.exports = pollingManager;
