/**
 * 统一积木数据管理器
 * 整合所有积木数据的获取、存储、同步逻辑
 */

class BrickManager {
  constructor() {
    this.initialized = false;
    this.bricks = [];
    this.lastSyncTime = null;
    this.syncInProgress = false;
  }

  /**
   * 初始化积木管理器
   */
  async init() {
    if (this.initialized) return;

    console.log('🧱 初始化积木数据管理器...');

    try {
      await this.loadBricks();
      this.initialized = true;
      console.log('✅ 积木数据管理器初始化完成');
    } catch (error) {
      console.error('❌ 积木数据管理器初始化失败:', error);
    }
  }

  /**
   * 统一加载积木数据
   * 优先级：全局数据 > 本地存储 > resumeTasks提取 > 云数据库 > 默认数据
   */
  async loadBricks() {
    console.log('📦 开始加载积木数据...');

    // 1. 尝试从全局数据获取
    const app = getApp();
    if (app.globalData && app.globalData.bricks && app.globalData.bricks.length > 0) {
      this.bricks = this.normalizeBricks(app.globalData.bricks);
      console.log(`✅ 从全局数据获取积木: ${this.bricks.length} 个`);
      return this.bricks;
    }

    // 2. 尝试从本地存储获取
    try {
      const localBricks = wx.getStorageSync('bricks');
      if (localBricks && localBricks.length > 0) {
        this.bricks = this.normalizeBricks(localBricks);
        console.log(`✅ 从本地存储获取积木: ${this.bricks.length} 个`);
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 本地存储获取积木失败:', error);
    }

    // 3. 从resumeTasks提取积木数据（核心修复）
    try {
      const extractedBricks = await this.extractBricksFromTasks();
      if (extractedBricks.length > 0) {
        this.bricks = this.normalizeBricks(extractedBricks);
        console.log(`✅ 从resumeTasks提取积木: ${this.bricks.length} 个`);
        this.saveToLocal();
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 从resumeTasks提取积木失败:', error);
    }

    // 4. 尝试从云数据库bricks集合获取
    try {
      const cloudBricks = await this.loadFromCloudDatabase();
      if (cloudBricks.length > 0) {
        this.bricks = this.normalizeBricks(cloudBricks);
        console.log(`✅ 从云数据库获取积木: ${this.bricks.length} 个`);
        this.saveToLocal();
        this.updateGlobalData();
        return this.bricks;
      }
    } catch (error) {
      console.warn('⚠️ 云数据库获取积木失败:', error);
    }

    // 5. 使用默认积木数据
    console.log('🔄 使用默认积木数据');
    this.bricks = this.normalizeBricks(this.getDefaultBricks());
    this.updateGlobalData();
    return this.bricks;
  }

  /**
   * 从resumeTasks中提取积木数据（核心功能）
   */
  async extractBricksFromTasks() {
    console.log('🔍 开始从resumeTasks提取积木数据...');

    const userId = wx.getStorageSync('userId') || wx.getStorageSync('openid');
    if (!userId) {
      console.warn('⚠️ 未找到用户ID，无法提取积木数据');
      return [];
    }

    try {
      const db = wx.cloud.database();
      const tasksResult = await db.collection('resumeTasks').where({
        userId: userId,
        status: 'completed'
      }).orderBy('completedAt', 'desc').get();

      console.log(`📋 找到 ${tasksResult.data.length} 个已完成的任务`);

      const extractedBricks = [];
      for (const task of tasksResult.data) {
        if (task.result && task.result.data && task.result.data.bricks) {
          const taskBricks = task.result.data.bricks.map(brick => ({
            ...brick,
            // 确保必要字段
            id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            sourceTaskId: task.taskId,
            extractedAt: new Date().toISOString(),
            userId: userId
          }));
          extractedBricks.push(...taskBricks);
        }
      }

      console.log(`🎯 成功提取 ${extractedBricks.length} 个积木`);
      return extractedBricks;

    } catch (error) {
      console.error('❌ 从resumeTasks提取积木失败:', error);
      return [];
    }
  }

  /**
   * 从云数据库bricks集合加载
   */
  async loadFromCloudDatabase() {
    try {
      const db = wx.cloud.database();
      const userId = wx.getStorageSync('userId') || wx.getStorageSync('openid');

      let query = db.collection('bricks');
      if (userId) {
        query = query.where({ userId });
      }

      const result = await query.limit(100).orderBy('createTime', 'desc').get();
      return result.data || [];
    } catch (error) {
      console.error('❌ 云数据库加载积木失败:', error);
      return [];
    }
  }

  /**
   * 获取默认积木数据
   */
  getDefaultBricks() {
    return [
      {
        id: 'default_1',
        category: 'personal',
        title: '个人信息',
        description: '请上传简历或手动添加个人信息，包括姓名、联系方式、地址等基本信息',
        content: '暂无积木数据',
        type: 'text',
        keywords: ['个人信息', '联系方式', '基本信息'],
        confidence: 1.0,
        usageCount: 0,
        level: '基础',
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        source: 'default',
        isDefault: true
      }
    ];
  }

  /**
   * 保存到本地存储
   */
  saveToLocal() {
    try {
      wx.setStorageSync('bricks', this.bricks);
      wx.setStorageSync('bricks_sync_time', new Date().toISOString());
      console.log('✅ 积木数据已保存到本地存储');
    } catch (error) {
      console.error('❌ 保存积木到本地存储失败:', error);
    }
  }

  /**
   * 更新全局数据
   */
  updateGlobalData() {
    try {
      const app = getApp();
      if (app.globalData) {
        app.globalData.bricks = this.bricks;
        console.log('✅ 积木数据已更新到全局数据');
      }
      if (app.store) {
        app.store.setState('bricks.list', this.bricks);
        console.log('✅ 积木数据已更新到状态管理器');
      }
    } catch (error) {
      console.error('❌ 更新全局数据失败:', error);
    }
  }

  /**
   * 标准化积木数据结构
   */
  normalizeBrick(brick) {
    // 检查是否是无效的内容
    const isInvalidContent = (content) => {
      if (!content || typeof content !== 'string') return true;
      const invalidTexts = ['暂无积木数据', '暂无数据', '无数据', ''];
      return invalidTexts.includes(content.trim());
    };

    // 根据分类生成默认标题和描述
    const getDefaultTitleAndDesc = (category) => {
      const defaults = {
        'personal': {
          title: '个人信息',
          description: '请上传简历或手动添加个人信息，包括姓名、联系方式、地址等基本信息'
        },
        'education': {
          title: '教育背景',
          description: '请添加您的教育经历，包括学校、专业、学历等信息'
        },
        'experience': {
          title: '工作经历',
          description: '请添加您的工作经验，包括公司、职位、工作内容等信息'
        },
        'project': {
          title: '项目经验',
          description: '请添加您的项目经历，包括项目名称、技术栈、成果等信息'
        },
        'skills': {
          title: '技能能力',
          description: '请添加您的技能和能力，包括技术技能、软技能等'
        }
      };

      return defaults[category] || {
        title: '能力积木',
        description: '请添加您的能力和经验描述'
      };
    };

    // 获取有效的标题
    const getValidTitle = () => {
      if (brick.title && !isInvalidContent(brick.title)) {
        return brick.title;
      }
      if (brick.content && !isInvalidContent(brick.content)) {
        return brick.content;
      }
      return getDefaultTitleAndDesc(brick.category).title;
    };

    // 获取有效的描述
    const getValidDescription = () => {
      if (brick.description && !isInvalidContent(brick.description)) {
        return brick.description;
      }
      if (brick.content && !isInvalidContent(brick.content) && brick.content !== getValidTitle()) {
        return brick.content;
      }
      return getDefaultTitleAndDesc(brick.category).description;
    };

    return {
      ...brick,
      // 确保必要字段存在且有效
      id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: getValidTitle(),
      description: getValidDescription(),
      category: brick.category || 'other',
      keywords: brick.keywords || [],
      confidence: brick.confidence || 0.8,
      usageCount: brick.usageCount || 0,
      level: brick.level || '基础',
      createTime: brick.createTime || new Date().toISOString(),
      updateTime: brick.updateTime || new Date().toISOString(),
      source: brick.source || 'unknown'
    };
  }

  /**
   * 标准化积木数组
   */
  normalizeBricks(bricks) {
    if (!Array.isArray(bricks)) return [];
    return bricks.map(brick => this.normalizeBrick(brick));
  }

  /**
   * 获取积木数据
   */
  async getBricks() {
    if (!this.initialized) {
      await this.init();
    }
    // 返回前进行数据标准化
    this.bricks = this.normalizeBricks(this.bricks);
    return this.bricks;
  }

  /**
   * 获取积木数量
   */
  async getBricksCount() {
    const bricks = await this.getBricks();
    return bricks.length;
  }

  /**
   * 强制刷新积木数据
   */
  async refresh() {
    console.log('🔄 强制刷新积木数据...');
    this.bricks = [];
    this.initialized = false;
    await this.init();
    return this.bricks;
  }

  /**
   * 清理并修复积木数据
   */
  async cleanAndRepair() {
    console.log('🧹 开始清理并修复积木数据...');

    try {
      // 检查是否有"暂无积木数据"的问题
      const hasInvalidData = this.bricks.some(brick =>
        brick.title === '暂无积木数据' ||
        brick.description === '暂无积木数据' ||
        brick.content === '暂无积木数据'
      );

      if (hasInvalidData) {
        console.log('🔧 检测到"暂无积木数据"问题，执行强制修复...');

        // 清理本地存储中的旧数据
        wx.removeStorageSync('bricks');
        wx.removeStorageSync('bricks_sync_time');

        // 清理全局数据
        const app = getApp();
        if (app.globalData) {
          app.globalData.bricks = [];
        }

        // 重新初始化
        this.bricks = [];
        this.initialized = false;
        await this.init();
      } else {
        console.log('✅ 数据正常，仅进行标准化处理');
        // 仅进行数据标准化
        this.bricks = this.normalizeBricks(this.bricks);
        this.saveToLocal();
        this.updateGlobalData();
      }

      console.log('✅ 积木数据清理修复完成');
      return this.bricks;
    } catch (error) {
      console.error('❌ 积木数据清理修复失败:', error);
      return [];
    }
  }

  /**
   * 强制重新生成默认积木（专门处理"暂无积木数据"问题）
   */
  async forceRegenerateDefaults() {
    console.log('🔄 强制重新生成默认积木数据...');

    try {
      // 完全清理所有数据
      wx.removeStorageSync('bricks');
      wx.removeStorageSync('bricks_sync_time');

      const app = getApp();
      if (app.globalData) {
        app.globalData.bricks = [];
      }

      // 重置管理器状态
      this.bricks = [];
      this.initialized = false;

      // 直接使用默认积木数据
      this.bricks = this.getDefaultBricks();

      // 应用标准化处理
      this.bricks = this.normalizeBricks(this.bricks);

      // 保存数据
      this.saveToLocal();
      this.updateGlobalData();
      this.initialized = true;

      console.log('✅ 默认积木数据重新生成完成');
      return this.bricks;
    } catch (error) {
      console.error('❌ 强制重新生成默认积木失败:', error);
      return [];
    }
  }

  /**
   * 添加新积木
   */
  async addBrick(brick) {
    const newBrick = {
      ...brick,
      id: brick.id || `brick_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createTime: new Date().toISOString(),
      userId: wx.getStorageSync('userId') || wx.getStorageSync('openid')
    };

    this.bricks.push(newBrick);
    this.saveToLocal();
    this.updateGlobalData();

    console.log('✅ 新积木已添加:', newBrick.id);
    return newBrick;
  }
}

// 创建全局单例
const brickManager = new BrickManager();

module.exports = brickManager;
