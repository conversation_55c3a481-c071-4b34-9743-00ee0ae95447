/**
 * SCF HTTP API 请求工具
 * 专门用于调用腾讯云SCF函数的HTTP接口
 */

const MOCK_MODE = false; // 修复：关闭模拟模式，使用真实SCF函数

class SCFRequestService {
  constructor() {
    // 修复：直接使用SCF Function URLs，不依赖API网关
    this.scfUrls = {
      resumeWorker: 'http://1341667342-21fo417pqr.ap-guangzhou.tencentscf.com',
      jdWorker: 'http://1341667342-4p9m4skdlh.ap-guangzhou.tencentscf.com',
      cvGenerator: 'http://1341667342-jcyribeze9.ap-guangzhou.tencentscf.com'
    };

    // 请求超时配置
    this.timeout = 90000; // 90秒超时，适配AI处理时间
  }

  /**
   * 修复：使用TCB云函数替代SCF函数调用
   * @param {string} functionName - TCB云函数名称
   * @param {object} data - 请求数据
   * @returns {Promise} 请求结果
   */
  async requestTCB(functionName, data = {}) {
    // TCB云函数名称映射
    const tcbFunctionMap = {
      'resumeWorker': 'resumeWorker',
      'intelligentResumeGenerator': 'intelligentResumeGenerator'
    };

    const tcbFunctionName = tcbFunctionMap[functionName];
    if (!tcbFunctionName) {
      throw new Error(`未知的TCB云函数: ${functionName}`);
    }

    const userInfo = wx.getStorageSync('userInfo') || {};
    const userId = wx.getStorageSync('userId') || 'anonymous';

    // 确保数据正确序列化，特别是包含中文字符的情况
    let requestData = {
      ...data,
      userInfo: userInfo,
      requestId: this.generateRequestId(),
      timestamp: new Date().toISOString()
    };

    // JSON序列化验证，确保中文字符正确处理
    try {
      const jsonString = JSON.stringify(requestData);
      requestData = JSON.parse(jsonString);
    } catch (jsonError) {
      console.warn('⚠️ JSON序列化警告:', jsonError);
    }

    console.log(`📡 TCB云函数请求: ${tcbFunctionName}`, {
      functionName: tcbFunctionName,
      dataSize: JSON.stringify(requestData).length
    });

    try {
      const result = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: tcbFunctionName,
          data: requestData,
          timeout: this.timeout,
          success: resolve,
          fail: reject
        });
      });

      console.log(`✅ TCB云函数响应: ${tcbFunctionName}`, {
        hasResult: !!result.result,
        success: result.result?.success
      });

      // 正确解析TCB云函数返回的结果
      if (result.result) {
        // 检查是否有statusCode和body（HTTP格式响应）
        if (result.result.statusCode === 200 && result.result.body) {
          const bodyData = JSON.parse(result.result.body);
          if (bodyData.success && bodyData.data) {
            return bodyData;
          } else {
            throw new Error(bodyData.error || '请求失败');
          }
        }
        // 检查直接的success格式
        else if (result.result.success) {
          return result.result;
        }
        // 其他错误情况
        else {
          throw new Error(result.result.message || result.result.error || '请求失败');
        }
      } else {
        throw new Error('TCB云函数调用失败：无返回结果');
      }
    } catch (error) {
      console.error(`❌ SCF请求失败 ${functionName}:`, error);

      // 提供友好的错误提示
      let errorMessage = '网络请求失败，请检查网络连接';

      if (error.message.includes('timeout')) {
        errorMessage = '请求超时，AI处理时间较长，请稍后重试';
      } else if (error.message.includes('404')) {
        errorMessage = 'SCF函数不存在，请联系管理员';
      } else if (error.message.includes('500')) {
        errorMessage = '服务器内部错误，请稍后重试';
      } else if (error.message) {
        errorMessage = error.message;
      }

      throw new Error(errorMessage);
    }
  }

  /**
   * 生成请求ID用于追踪
   */
  generateRequestId() {
    return `mp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取云存储文件内容 - 微信小程序环境优化版
   */
  async getFileContent(fileID) {
    try {
      // 检查是否在微信小程序环境中
      if (typeof wx === 'undefined' || !wx.cloud) {
        throw new Error('当前环境不支持微信云开发功能');
      }

      // 获取文件临时下载链接
      const tempUrlResult = await wx.cloud.getTempFileURL({
        fileList: [fileID]
      });

      if (!tempUrlResult.fileList || tempUrlResult.fileList.length === 0) {
        throw new Error('获取文件下载链接失败');
      }

      const downloadUrl = tempUrlResult.fileList[0].tempFileURL;

      // 检查下载链接是否有效
      if (!downloadUrl) {
        throw new Error('文件下载链接无效');
      }

      // 下载文件内容
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: downloadUrl,
          method: 'GET',
          responseType: 'text',
          timeout: 30000, // 30秒超时
          success: resolve,
          fail: reject
        });
      });

      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(`下载文件失败，状态码: ${response.statusCode}`);
      }
    } catch (error) {
      console.error('❌ 获取文件内容失败:', error);
      throw new Error(`无法读取文件内容: ${error.message}`);
    }
  }

  /**
   * 根据文件名获取文件类型
   */
  getFileType(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();
    const typeMap = {
      'pdf': 'pdf',
      'txt': 'txt',
      'doc': 'doc',
      'docx': 'docx'
    };
    return typeMap[extension] || 'txt';
  }

  /**
   * PDF文本预处理：专门处理PDF转换后的文本
   */
  preprocessPDFText(rawText) {
    if (!rawText || typeof rawText !== 'string') {
      return '';
    }

    console.log('🧹 开始PDF文本预处理', { originalLength: rawText.length });

    let processedText = rawText;

    // 1. PDF特有的清理：移除PDF转换产生的噪音
    processedText = processedText
      .replace(/Page \d+ of \d+/gi, '')  // 移除页码信息
      .replace(/^\d+\s*$/gm, '')         // 移除单独的数字行（可能是页码）
      .replace(/^[^\w\u4e00-\u9fff\s]*$/gm, '') // 移除只包含特殊字符的行
      .replace(/\f/g, '\n')              // 换页符转换行
      .replace(/\v/g, '\n');             // 垂直制表符转换行

    // 2. 调用通用文本预处理
    processedText = this.preprocessText(processedText);

    console.log('✅ PDF文本预处理完成', {
      originalLength: rawText.length,
      processedLength: processedText.length,
      compressionRatio: Math.round((1 - processedText.length / rawText.length) * 100) + '%'
    });

    return processedText;
  }

  /**
   * 通用文本预处理：清理和格式化文本
   */
  preprocessText(rawText) {
    if (!rawText || typeof rawText !== 'string') {
      return '';
    }

    let processedText = rawText;

    // 1. 统一换行符和空白字符
    processedText = processedText
      .replace(/\r\n/g, '\n')    // 统一换行符
      .replace(/\r/g, '\n')      // 统一换行符
      .replace(/\t/g, ' ')       // 制表符转空格
      .replace(/[ ]{2,}/g, ' ')  // 多个空格合并为一个
      .replace(/\n{3,}/g, '\n\n') // 多个换行合并为两个
      .replace(/[ ]*\n[ ]*/g, '\n') // 移除换行前后的空格
      .replace(/^\s+|\s+$/g, ''); // 移除首尾空白

    // 2. 处理中文字符编码问题
    try {
      // 确保中文字符正确编码
      processedText = processedText.normalize('NFC');
    } catch (e) {
      console.warn('⚠️ 文本标准化失败:', e.message);
    }

    // 3. 限制文本长度（与resumeWorker保持一致）
    const MAX_TEXT_LENGTH = 50000;
    const TRUNCATION_NOTE = '\n\n[注：文本内容已截取，如需完整分析请分段处理]';

    if (processedText.length > MAX_TEXT_LENGTH) {
      console.log(`⚠️ 文本过长 (${processedText.length} 字符)，截取前 ${MAX_TEXT_LENGTH} 字符`);
      // 确保截取后加上提示信息不会超过限制
      const maxContentLength = MAX_TEXT_LENGTH - TRUNCATION_NOTE.length;
      processedText = processedText.substring(0, maxContentLength) + TRUNCATION_NOTE;
    }

    // 4. 最终清理
    processedText = processedText.trim();

    return processedText;
  }

  /**
   * 异步简历解析 - 调用resumeTaskSubmitter创建异步任务，并立即触发resumeWorker
   */
  async resumeGateway(fileID, fileName, userInfo = {}) {
    try {
      console.log('📄 开始异步简历处理流程', { fileName, fileID });

      const requestData = {
        fileId: fileID,
        fileName: fileName,
        fileType: this.getFileType(fileName),
        userId: wx.getStorageSync('userId') || 'anonymous',
        userInfo: userInfo
      };

      // 第一步：调用resumeTaskSubmitter云函数创建异步任务
      const result = await wx.cloud.callFunction({
        name: 'resumeTaskSubmitter',
        data: requestData
      });

      console.log('📋 任务提交结果:', result);

      let taskResponse;
      if (result.result && result.result.body) {
        const response = JSON.parse(result.result.body);
        if (response.success) {
          taskResponse = {
            success: true,
            taskId: response.data.taskId,
            status: response.data.status,
            estimatedTime: response.data.estimatedTime,
            message: response.data.message
          };
        } else {
          throw new Error(response.error || '创建任务失败');
        }
      } else if (result.result && result.result.success) {
        taskResponse = {
          success: true,
          taskId: result.result.data.taskId,
          status: result.result.data.status,
          estimatedTime: result.result.data.estimatedTime,
          message: result.result.data.message
        };
      } else {
        throw new Error('创建任务失败');
      }

      // 🚀 第二步：立即触发resumeWorker进行异步处理（修复关键步骤）
      if (taskResponse.success && taskResponse.taskId) {
        console.log('🚀 立即触发resumeWorker进行异步处理，taskId:', taskResponse.taskId);

        // 异步触发resumeWorker，不等待结果
        wx.cloud.callFunction({
          name: 'resumeWorker',
          data: {
            taskId: taskResponse.taskId,
            fileId: fileID,
            fileName: fileName,
            fileType: this.getFileType(fileName),
            triggerMode: 'frontend_auto'
          }
        }).then(() => {
          console.log('✅ resumeWorker触发成功');
        }).catch(error => {
          console.warn('⚠️ resumeWorker触发失败，但任务已创建:', error);
        });
      }

      return taskResponse;

    } catch (error) {
      console.error('❌ 创建异步任务失败:', error);
      return {
        success: false,
        error: error.message || '创建异步任务失败'
      };
    }
  }

  /**
   * 获取任务进度 - 调用resumeTaskQuery云函数
   */
  async getTaskProgress(taskId) {
    try {
      console.log('🔍 查询任务状态:', taskId);

      const result = await wx.cloud.callFunction({
        name: 'resumeTaskQuery',
        data: {
          taskId: taskId,
          userId: wx.getStorageSync('userId') || 'anonymous'
        }
      });

      console.log('📋 任务状态查询结果:', result);

      if (result.result && result.result.body) {
        const response = JSON.parse(result.result.body);
        return response;
      } else if (result.result && result.result.success) {
        return result.result;
      } else {
        throw new Error('查询任务状态失败');
      }
    } catch (error) {
      console.error('❌ 查询任务状态异常:', error);
      return {
        success: false,
        error: error.message || '查询任务状态失败'
      };
    }
  }

  /**
   * 修复：JD分析 - 使用intelligentResumeGenerator云函数
   */
  async analyzeJd(jdContent, companyName = '', positionName = '') {
    const result = await this.requestTCB('intelligentResumeGenerator', {
      jdContent: jdContent,
      companyName: companyName,
      positionName: positionName,
      userBricks: [], // 空积木数组，仅进行JD分析
      personalInfo: {}, // 空个人信息
      templateId: 'default'
    });

    // 只返回JD分析部分
    if (result.success && result.data && result.data.jdAnalysis) {
      return {
        success: true,
        data: result.data.jdAnalysis
      };
    }
    return result;
  }

  /**
   * 修复：简历生成 - 使用intelligentResumeGenerator云函数
   */
  async generateResume(jdAnalysis, userInfo, targetPosition, companyName) {
    const result = await this.requestTCB('intelligentResumeGenerator', {
      jdContent: jdAnalysis?.jdContent || '',
      companyName: companyName || jdAnalysis?.companyName || '',
      positionName: targetPosition || jdAnalysis?.positionName || '',
      userBricks: userInfo || [],
      personalInfo: userInfo || {},
      templateId: 'default'
    });

    // 只返回简历生成部分
    if (result.success && result.data && result.data.resume) {
      return {
        success: true,
        data: result.data.resume
      };
    }
    return result;
  }

  /**
   * 用户登录
   * 替换原来的 user-login 云函数
   */
  async userLogin(code) {
    if (MOCK_MODE) {
      console.warn("登录接口处于模拟模式");
      const mockData = {
        success: true,
        message: "模拟登录成功",
        token: "mock-token-for-development-*********",
        user: {
          _id: 'mock-user-id-001',
          openid: 'mock-openid-abcdefg',
          nickName: '模拟用户',
          avatarUrl: '/static/icons/profile-active.png',
          brick_count: 10,
          usage_count: 20,
          last_login_time: new Date().toISOString()
        }
      };

      wx.setStorageSync('token', mockData.token);
      wx.setStorageSync('user', mockData.user);

      return Promise.resolve(mockData);
    }

    const endpoint = '/user-login';
    const data = { code };

    return await this.request(endpoint, {
      data: data
    });
  }

  /**
   * 用户统计
   * 替换原来的 user-stats 云函数
   */
  async getUserStats(userId) {
    return await this.request('/user-stats', {
      data: {
        userId
      }
    });
  }

  /**
   * 积木管理
   * 替换原来的 bricks-manage 云函数
   */
  async manageBricks(action, bricks = null, userId = null) {
    return await this.request('/bricks-manage', {
      data: {
        action,
        bricks,
        userId
      }
    });
  }

  /**
   * DeepSeek测试 - 基础版
   * 替换原来的 deepseek-resume-test 云函数
   */
  async deepseekResumeTest() {
    return await this.request('/deepseek-resume-test', {
      data: {}
    });
  }

  /**
   * DeepSeek测试 - 优化版
   * 替换原来的 deepseek-resume-optimized 云函数
   */
  async deepseekResumeOptimized(resumeContent) {
    return await this.request('/deepseek-resume-optimized', {
      data: {
        resumeContent
      }
    });
  }

  /**
   * 检查SCF服务状态
   */
  async checkServiceHealth() {
    try {
      const response = await this.request('/health', {
        method: 'GET',
        data: {}
      });
      return {
        available: true,
        response
      };
    } catch (error) {
      return {
        available: false,
        error: error.message
      };
    }
  }
}

// 创建全局实例
const scfRequest = new SCFRequestService();

// 兼容性处理：注册到全局和app实例
if (typeof global !== 'undefined') {
  global.scfRequest = scfRequest;
}

// 导出服务
module.exports = scfRequest; 