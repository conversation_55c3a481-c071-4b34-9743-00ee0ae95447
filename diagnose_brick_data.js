/**
 * 积木数据深度诊断脚本
 * 在微信开发者工具的Console中运行此脚本来诊断当前积木数据问题
 */

// 深度诊断当前积木数据
async function diagnoseBrickData() {
  console.log('🔍 开始深度诊断积木数据...');
  
  try {
    // 1. 检查本地存储数据
    console.log('\n📱 检查本地存储数据:');
    const localBricks = wx.getStorageSync('bricks') || [];
    console.log('本地存储积木数量:', localBricks.length);
    
    if (localBricks.length > 0) {
      localBricks.forEach((brick, index) => {
        console.log(`本地积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          description: brick.description,
          content: brick.content,
          category: brick.category,
          source: brick.source,
          isDefault: brick.isDefault
        });
      });
    }
    
    // 2. 检查全局数据
    console.log('\n🌐 检查全局数据:');
    const app = getApp();
    const globalBricks = app.globalData?.bricks || [];
    console.log('全局数据积木数量:', globalBricks.length);
    
    if (globalBricks.length > 0) {
      globalBricks.forEach((brick, index) => {
        console.log(`全局积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          description: brick.description,
          content: brick.content,
          category: brick.category,
          source: brick.source,
          isDefault: brick.isDefault
        });
      });
    }
    
    // 3. 检查BrickManager数据
    console.log('\n🧱 检查BrickManager数据:');
    const BrickManager = require('utils/brick-manager.js');
    const managerBricks = await BrickManager.getBricks();
    console.log('BrickManager积木数量:', managerBricks.length);
    
    if (managerBricks.length > 0) {
      managerBricks.forEach((brick, index) => {
        console.log(`Manager积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          description: brick.description,
          content: brick.content,
          category: brick.category,
          source: brick.source,
          isDefault: brick.isDefault
        });
      });
    }
    
    // 4. 检查页面数据
    console.log('\n📄 检查页面数据:');
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/bricks/bricks') {
      const pageBricks = currentPage.data.bricks || [];
      const filteredBricks = currentPage.data.filteredBricks || [];
      const personalCount = currentPage.data.personalCount || 0;
      
      console.log('页面积木数量:', pageBricks.length);
      console.log('筛选后积木数量:', filteredBricks.length);
      console.log('个人信息积木数量:', personalCount);
      
      // 找出个人信息积木
      const personalBricks = pageBricks.filter(brick => 
        brick.category === 'personal' || 
        brick.category === '个人' || 
        brick.category === '个人信息'
      );
      
      console.log('页面中的个人信息积木:');
      personalBricks.forEach((brick, index) => {
        console.log(`页面个人积木 ${index + 1}:`, {
          id: brick.id,
          title: brick.title,
          description: brick.description,
          content: brick.content,
          category: brick.category,
          source: brick.source,
          isDefault: brick.isDefault
        });
      });
    }
    
    // 5. 分析问题
    console.log('\n🔍 问题分析:');
    
    // 检查是否有"暂无积木数据"的问题
    const allSources = [
      { name: '本地存储', data: localBricks },
      { name: '全局数据', data: globalBricks },
      { name: 'BrickManager', data: managerBricks }
    ];
    
    allSources.forEach(source => {
      const problematicBricks = source.data.filter(brick => 
        brick.title === '暂无积木数据' || 
        brick.description === '暂无积木数据' ||
        brick.content === '暂无积木数据'
      );
      
      if (problematicBricks.length > 0) {
        console.log(`❌ ${source.name}中发现问题积木:`, problematicBricks.length, '个');
        problematicBricks.forEach(brick => {
          console.log('问题积木:', {
            id: brick.id,
            title: brick.title,
            description: brick.description,
            content: brick.content,
            source: brick.source
          });
        });
      } else {
        console.log(`✅ ${source.name}中无问题积木`);
      }
    });
    
    return {
      localBricks,
      globalBricks,
      managerBricks,
      hasProblems: allSources.some(source => 
        source.data.some(brick => 
          brick.title === '暂无积木数据' || 
          brick.description === '暂无积木数据'
        )
      )
    };
    
  } catch (error) {
    console.error('❌ 诊断失败:', error);
    return null;
  }
}

// 强制清理并重新生成正确数据
async function forceCleanAndRegenerate() {
  console.log('🧹 开始强制清理并重新生成数据...');
  
  try {
    // 1. 清理所有存储
    console.log('🗑️ 清理所有存储数据...');
    wx.removeStorageSync('bricks');
    wx.removeStorageSync('bricks_sync_time');
    
    // 2. 清理全局数据
    console.log('🌐 清理全局数据...');
    const app = getApp();
    if (app.globalData) {
      app.globalData.bricks = [];
    }
    
    // 3. 重置BrickManager
    console.log('🔄 重置BrickManager...');
    const BrickManager = require('utils/brick-manager.js');
    BrickManager.bricks = [];
    BrickManager.initialized = false;
    
    // 4. 手动创建正确的个人信息积木
    console.log('✨ 创建正确的个人信息积木...');
    const correctPersonalBrick = {
      id: `personal_${Date.now()}`,
      category: 'personal',
      title: '个人信息',
      description: '请上传简历或手动添加个人信息，包括姓名、联系方式、地址等基本信息',
      content: '个人基本信息积木',
      keywords: ['个人信息', '联系方式', '基本信息'],
      confidence: 1.0,
      usageCount: 0,
      level: '基础',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      source: 'manual_fix',
      isDefault: false
    };
    
    // 5. 保存正确的积木数据
    const correctBricks = [correctPersonalBrick];
    
    // 保存到本地存储
    wx.setStorageSync('bricks', correctBricks);
    
    // 保存到全局数据
    app.globalData.bricks = correctBricks;
    
    // 更新BrickManager
    BrickManager.bricks = correctBricks;
    BrickManager.initialized = true;
    
    console.log('✅ 数据重新生成完成');
    
    // 6. 刷新页面数据
    console.log('🔄 刷新页面数据...');
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/bricks/bricks') {
      // 直接更新页面数据
      currentPage.setData({
        bricks: correctBricks,
        filteredBricks: correctBricks
      });
      
      // 更新统计
      currentPage.updateCounts();
      currentPage.filterBricks();
      
      console.log('✅ 页面数据已刷新');
    }
    
    return correctBricks;
    
  } catch (error) {
    console.error('❌ 强制清理重新生成失败:', error);
    return null;
  }
}

// 验证修复效果
function verifyFix() {
  console.log('🧪 验证修复效果...');
  
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    if (currentPage.route === 'pages/bricks/bricks') {
      const bricks = currentPage.data.bricks || [];
      const personalBricks = bricks.filter(brick => brick.category === 'personal');
      
      console.log('验证结果:');
      console.log('- 总积木数:', bricks.length);
      console.log('- 个人信息积木数:', personalBricks.length);
      
      if (personalBricks.length > 0) {
        const brick = personalBricks[0];
        console.log('- 个人信息积木标题:', brick.title);
        console.log('- 个人信息积木描述:', brick.description);
        
        if (brick.title !== '暂无积木数据' && brick.description !== '暂无积木数据') {
          console.log('🎉 修复成功！个人信息积木显示正常');
          return true;
        } else {
          console.log('❌ 修复失败，仍显示"暂无积木数据"');
          return false;
        }
      } else {
        console.log('⚠️ 未找到个人信息积木');
        return false;
      }
    } else {
      console.log('⚠️ 当前不在积木库页面');
      return false;
    }
  } catch (error) {
    console.error('❌ 验证失败:', error);
    return false;
  }
}

// 完整修复流程
async function completeFixFlow() {
  console.log('🚀 开始完整修复流程...');
  
  // 1. 诊断当前问题
  const diagnosis = await diagnoseBrickData();
  
  if (diagnosis && diagnosis.hasProblems) {
    console.log('🔧 检测到问题，开始修复...');
    
    // 2. 强制清理并重新生成
    const newBricks = await forceCleanAndRegenerate();
    
    if (newBricks) {
      // 3. 验证修复效果
      setTimeout(() => {
        const success = verifyFix();
        if (success) {
          console.log('🎉 完整修复流程成功完成！');
        } else {
          console.log('❌ 修复流程完成但验证失败');
        }
      }, 1000);
    }
  } else {
    console.log('✅ 未检测到问题或诊断失败');
  }
}

// 导出函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    diagnoseBrickData,
    forceCleanAndRegenerate,
    verifyFix,
    completeFixFlow
  };
}

console.log('🔍 积木数据诊断脚本已加载');
console.log('💡 使用方法:');
console.log('1. 运行 diagnoseBrickData() 诊断当前数据');
console.log('2. 运行 forceCleanAndRegenerate() 强制修复');
console.log('3. 运行 verifyFix() 验证修复效果');
console.log('4. 运行 completeFixFlow() 执行完整修复流程');
