{"name": "ai-resume-optimization", "version": "1.0.0", "description": "AI-powered resume optimization system using serverless architecture", "main": "index.js", "scripts": {"init-db": "node scripts/init-database.js", "test-db": "node scripts/test-database.js", "verify-db": "node scripts/verify-schema.js", "test-env": "node scripts/test-env.js", "deploy": "node scripts/deploy-new-architecture.js", "setup-cmq": "node scripts/setup-cmq.js", "dev": "node index.js", "test": "node tests/run-tests.js", "test:unit": "node tests/run-tests.js --unit", "test:integration": "node tests/run-tests.js --integration", "test:performance": "node tests/run-tests.js --performance", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "analyze:complexity": "node tests/complexity-analysis.js", "analyze:performance": "node tests/performance-analysis.js", "analyze:all": "npm run analyze:complexity && npm run analyze:performance", "lint": "eslint . --fix", "test:cv-online": "jest tests/cv-content-validation.test.js --runInBand", "debug:pdf-download": "bash scripts/debug-download-pdf.sh"}, "dependencies": {"@cloudbase/adapter-node": "^1.0.2", "@cloudbase/js-sdk": "^2.18.3", "@cloudbase/node-sdk": "^3.10.1", "axios": "^1.10.0", "canvas": "^2.11.2", "cmq-sdk": "^1.0.2", "cos-nodejs-sdk-v5": "^2.12.2", "crypto": "^1.0.1", "formidable": "^3.5.1", "handlebars": "^4.7.8", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdf2pic": "^2.1.4", "task-master-ai": "^0.18.0", "tencentcloud-sdk-nodejs": "^4.0.0", "util": "^0.12.5", "uuid": "^9.0.0", "winston": "^3.11.0", "wx-server-sdk": "^3.0.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.8", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "pdfjs-dist": "^3.4.120", "supertest": "^6.3.3"}, "keywords": ["ai", "resume", "optimization", "serverless", "tencent-cloud", "scf"], "author": "AI Resume Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}